/**
 * @file SystemConfiguration.h
 * @brief System configuration management interface
 * <AUTHOR> Systems Team
 * @date 2025-01-15
 * @copyright Copyright (c) 2025 Advanced Embedded Solutions. All rights reserved.
 * 
 * This module provides comprehensive configuration management capabilities
 * including parameter validation, persistent storage, and runtime updates.
 */

#ifndef SYSTEM_CONFIGURATION_H
#define SYSTEM_CONFIGURATION_H

#include "SystemDefinitions.h"

/* Configuration Parameter Limits */
#define CONFIGURATION_RATIO_MIN          0.1f
#define CONFIGURATION_RATIO_MAX          10.0f
#define CONFIGURATION_LIMIT_MIN          0.0f
#define CONFIGURATION_LIMIT_MAX          100.0f
#define CONFIGURATION_DEVICE_ID_LENGTH   32

/**
 * @brief System configuration parameters structure
 * @details Contains all configurable system parameters
 */
typedef struct SystemConfigurationParameters
{
  float voltageRatio;                  /**< Voltage conversion ratio */
  float voltageLimit;                  /**< Voltage limit threshold */
  SamplingIntervalType samplingCycle;  /**< Sampling interval configuration */
  char deviceIdentifier[CONFIGURATION_DEVICE_ID_LENGTH]; /**< Device ID string */
  uint32_t configurationCrc;           /**< Configuration integrity checksum */
  uint32_t configurationVersion;       /**< Configuration version number */
} SystemConfigurationParameters;

/* Public Function Declarations */

/**
 * @brief Initialize the system configuration manager
 * @details Sets up configuration system and loads parameters from storage
 * @param None
 * @return SystemConfigurationStatus Initialization result
 * @retval CONFIGURATION_SUCCESS Initialization completed successfully
 * @retval CONFIGURATION_FAILURE Initialization failed
 */
SystemConfigurationStatus InitializeSystemConfiguration(void);

/**
 * @brief Retrieve current configuration parameters
 * @details Gets a copy of the current system configuration
 * @param configParams Pointer to configuration structure to fill
 * @return SystemConfigurationStatus Operation result
 */
SystemConfigurationStatus RetrieveConfigurationParameters(
  SystemConfigurationParameters* configParams
);

/**
 * @brief Update configuration parameters
 * @details Updates system configuration with new values
 * @param configParams Pointer to new configuration parameters
 * @return SystemConfigurationStatus Update result
 */
SystemConfigurationStatus UpdateConfigurationParameters(
  const SystemConfigurationParameters* configParams
);

/**
 * @brief Save configuration to persistent storage
 * @details Writes current configuration to Flash memory
 * @param None
 * @return SystemConfigurationStatus Save operation result
 */
SystemConfigurationStatus SaveConfigurationToFlash(void);

/**
 * @brief Load configuration from persistent storage
 * @details Reads configuration from Flash memory
 * @param None
 * @return SystemConfigurationStatus Load operation result
 */
SystemConfigurationStatus LoadConfigurationFromFlash(void);

/**
 * @brief Reset configuration to default values
 * @details Restores factory default configuration
 * @param None
 * @return SystemConfigurationStatus Reset operation result
 */
SystemConfigurationStatus ResetConfigurationToDefault(void);

/**
 * @brief Validate ratio parameter
 * @details Checks if ratio value is within acceptable range
 * @param ratio Ratio value to validate
 * @return SystemConfigurationStatus Validation result
 */
SystemConfigurationStatus ValidateRatioParameter(float ratio);

/**
 * @brief Validate limit parameter
 * @details Checks if limit value is within acceptable range
 * @param limit Limit value to validate
 * @return SystemConfigurationStatus Validation result
 */
SystemConfigurationStatus ValidateLimitParameter(float limit);

/**
 * @brief Configure sampling cycle
 * @details Sets the system sampling interval
 * @param cycle New sampling cycle value
 * @return SystemConfigurationStatus Configuration result
 */
SystemConfigurationStatus ConfigureSamplingCycle(SamplingIntervalType cycle);

/**
 * @brief Retrieve current sampling cycle
 * @details Gets the current sampling interval setting
 * @param None
 * @return SamplingIntervalType Current sampling cycle
 */
SamplingIntervalType RetrieveSamplingCycle(void);

/**
 * @brief Calculate configuration CRC32
 * @details Computes integrity checksum for configuration data
 * @param configParams Pointer to configuration parameters
 * @return uint32_t Calculated CRC32 value
 */
uint32_t CalculateConfigurationCrc32(const SystemConfigurationParameters* configParams);

#endif /* SYSTEM_CONFIGURATION_H */