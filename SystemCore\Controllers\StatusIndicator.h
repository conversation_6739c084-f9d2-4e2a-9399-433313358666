/**
 * @file StatusIndicator.h
 * @brief System status LED indicator controller interface
 * <AUTHOR> Systems Team
 * @date 2025-01-15
 * @copyright Copyright (c) 2025 Advanced Embedded Solutions. All rights reserved.
 */

#ifndef STATUS_INDICATOR_H
#define STATUS_INDICATOR_H

#include "SystemDefinitions.h"

/* LED Configuration Constants */
#define STATUS_LED_COUNT                 8
#define LED_BLINK_INTERVAL_FAST_MS       200
#define LED_BLINK_INTERVAL_SLOW_MS       1000
#define LED_BLINK_INTERVAL_NORMAL_MS     500

/* LED State Definitions */
#define LED_STATE_OFF                    0
#define LED_STATE_ON                     1
#define LED_STATE_BLINKING               2

/* LED Index Definitions */
#define LED_INDEX_POWER                  0
#define LED_INDEX_STATUS                 1
#define LED_INDEX_SAMPLING               2
#define LED_INDEX_OVERLIMIT              3
#define LED_INDEX_COMMUNICATION          4
#define LED_INDEX_ERROR                  5
#define LED_INDEX_USER1                  6
#define LED_INDEX_USER2                  7

/**
 * @brief LED pattern configuration structure
 * @details Defines the behavior pattern for LED indicators
 */
typedef struct LedPatternConfiguration
{
  uint8_t ledState;                    /**< Current LED state */
  uint32_t blinkInterval;              /**< Blink interval in milliseconds */
  uint32_t onDuration;                 /**< On duration for blink pattern */
  uint32_t offDuration;                /**< Off duration for blink pattern */
  uint8_t blinkCount;                  /**< Number of blinks (0 = continuous) */
  uint8_t currentBlinkCount;           /**< Current blink counter */
} LedPatternConfiguration;

/**
 * @brief Status indicator context structure
 * @details Contains all state information for LED management
 */
typedef struct StatusIndicatorContext
{
  uint8_t m_indicatorStates[STATUS_LED_COUNT];           /**< LED state array */
  LedPatternConfiguration m_ledPatterns[STATUS_LED_COUNT]; /**< LED pattern configs */
  uint32_t m_primaryBlinkTimer;                          /**< Primary blink timer */
  uint8_t m_primaryBlinkState;                           /**< Primary blink state */
  uint32_t m_lastUpdateTime;                             /**< Last update timestamp */
} StatusIndicatorContext;

/* Public Function Declarations */

/**
 * @brief Initialize status indicator system
 * @details Sets up LED GPIO and initial states
 * @param None
 * @return DataAcquisitionStatus Initialization result
 */
DataAcquisitionStatus InitializeStatusIndicator(void);

/**
 * @brief Update status indicators
 * @details Main task function for LED management
 * @param None
 * @return None
 * @note This function should be called periodically by the task scheduler
 */
void UpdateStatusIndicators(void);

/**
 * @brief Display LED pattern
 * @details Updates LED states according to configured patterns
 * @param None
 * @return None
 */
void DisplayLedPattern(void);

/**
 * @brief Set LED state
 * @details Controls individual LED on/off state
 * @param ledIndex Index of LED to control
 * @param state New LED state (on/off/blinking)
 * @return DataAcquisitionStatus Operation result
 */
DataAcquisitionStatus SetLedState(uint8_t ledIndex, uint8_t state);

/**
 * @brief Configure LED blink pattern
 * @details Sets up blinking behavior for specific LED
 * @param ledIndex Index of LED to configure
 * @param interval Blink interval in milliseconds
 * @param onDuration Duration LED stays on
 * @param offDuration Duration LED stays off
 * @return DataAcquisitionStatus Configuration result
 */
DataAcquisitionStatus ConfigureLedBlinkPattern(
  uint8_t ledIndex,
  uint32_t interval,
  uint32_t onDuration,
  uint32_t offDuration
);

/**
 * @brief Set sampling status indication
 * @details Updates LEDs to show sampling state
 * @param isActive True if sampling is active
 * @return DataAcquisitionStatus Operation result
 */
DataAcquisitionStatus SetSamplingStatusIndication(uint8_t isActive);

/**
 * @brief Set overlimit status indication
 * @details Updates LEDs to show voltage overlimit condition
 * @param isOverlimit True if voltage is over limit
 * @return DataAcquisitionStatus Operation result
 */
DataAcquisitionStatus SetOverlimitStatusIndication(uint8_t isOverlimit);

/**
 * @brief Set communication status indication
 * @details Updates LEDs to show communication activity
 * @param isActive True if communication is active
 * @return DataAcquisitionStatus Operation result
 */
DataAcquisitionStatus SetCommunicationStatusIndication(uint8_t isActive);

/**
 * @brief Set error status indication
 * @details Updates LEDs to show error conditions
 * @param hasError True if system error is present
 * @return DataAcquisitionStatus Operation result
 */
DataAcquisitionStatus SetErrorStatusIndication(uint8_t hasError);

/**
 * @brief Get LED state
 * @details Retrieves current state of specific LED
 * @param ledIndex Index of LED to query
 * @return uint8_t Current LED state
 */
uint8_t GetLedState(uint8_t ledIndex);

/**
 * @brief Get status indicator context
 * @details Provides access to LED management state
 * @param None
 * @return StatusIndicatorContext* Pointer to indicator context
 */
StatusIndicatorContext* GetStatusIndicatorContext(void);

/**
 * @brief Test all LEDs
 * @details Performs LED functionality test sequence
 * @param None
 * @return DataAcquisitionStatus Test result
 */
DataAcquisitionStatus TestAllLeds(void);

#endif /* STATUS_INDICATOR_H */