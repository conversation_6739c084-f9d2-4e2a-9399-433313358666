/**
 * @file DataAcquisitionManager.h
 * @brief Data acquisition and sampling control manager interface
 * <AUTHOR> Systems Team
 * @date 2025-01-15
 * @copyright Copyright (c) 2025 Advanced Embedded Solutions. All rights reserved.
 */

#ifndef DATA_ACQUISITION_MANAGER_H
#define DATA_ACQUISITION_MANAGER_H

#include "SystemDefinitions.h"

/* Acquisition Control Constants */
#define ACQUISITION_VOLTAGE_THRESHOLD    5.0f
#define ACQUISITION_SAMPLE_HISTORY_SIZE  10
#define ACQUISITION_OVERLIMIT_DEBOUNCE   3

/**
 * @brief Data acquisition context structure
 * @details Contains all state information for data acquisition operations
 */
typedef struct DataAcquisitionContext
{
  DataAcquisitionState acquisitionState;          /**< Current acquisition state */
  SamplingIntervalType samplingInterval;          /**< Configured sampling interval */
  uint32_t lastSampleTimestamp;                   /**< Timestamp of last sample */
  uint32_t nextSampleTimestamp;                   /**< Timestamp for next sample */
  float currentVoltageReading;                    /**< Latest voltage measurement */
  float voltageThreshold;                         /**< Overlimit threshold value */
  uint8_t overlimitCondition;                     /**< Overlimit detection flag */
  uint8_t overlimitDebounceCounter;               /**< Overlimit debounce counter */
  uint32_t totalSampleCount;                      /**< Total samples collected */
  uint32_t overlimitEventCount;                   /**< Number of overlimit events */
  uint8_t indicatorBlinkState;                    /**< LED blink state for sampling */
} DataAcquisitionContext;

/* Public Function Declarations */

/**
 * @brief Initialize data acquisition manager
 * @details Sets up acquisition system and default parameters
 * @param None
 * @return DataAcquisitionStatus Initialization result
 */
DataAcquisitionStatus InitializeDataAcquisition(void);

/**
 * @brief Begin data collection operations
 * @details Starts the data acquisition process
 * @param None
 * @return DataAcquisitionStatus Start operation result
 */
DataAcquisitionStatus BeginDataCollection(void);

/**
 * @brief Terminate data collection operations
 * @details Stops the data acquisition process
 * @param None
 * @return DataAcquisitionStatus Stop operation result
 */
DataAcquisitionStatus TerminateDataCollection(void);

/**
 * @brief Configure sampling interval
 * @details Sets the time interval between samples
 * @param interval New sampling interval type
 * @return DataAcquisitionStatus Configuration result
 */
DataAcquisitionStatus ConfigureSamplingInterval(SamplingIntervalType interval);

/**
 * @brief Retrieve acquisition state
 * @details Gets current data acquisition state
 * @param None
 * @return DataAcquisitionState Current acquisition state
 */
DataAcquisitionState RetrieveAcquisitionState(void);

/**
 * @brief Retrieve sampling interval
 * @details Gets current sampling interval configuration
 * @param None
 * @return SamplingIntervalType Current sampling interval
 */
SamplingIntervalType RetrieveSamplingInterval(void);

/**
 * @brief Check if sampling should be performed
 * @details Determines if it's time for next sample based on interval
 * @param None
 * @return uint8_t 1 if sampling should occur, 0 otherwise
 */
uint8_t ShouldPerformSampling(void);

/**
 * @brief Update indicator blink state
 * @details Updates LED blinking for sampling indication
 * @param None
 * @return None
 */
void UpdateIndicatorBlink(void);

/**
 * @brief Retrieve voltage reading
 * @details Gets the latest voltage measurement
 * @param None
 * @return float Current voltage reading in volts
 */
float RetrieveVoltageReading(void);

/**
 * @brief Check voltage overlimit condition
 * @details Determines if voltage exceeds configured threshold
 * @param voltage Voltage value to check
 * @return uint8_t 1 if overlimit, 0 otherwise
 */
uint8_t CheckVoltageOverlimit(float voltage);

/**
 * @brief Execute sampling task
 * @details Main sampling task function
 * @param None
 * @return None
 * @note This function should be called by the task scheduler
 */
void ExecuteSamplingTask(void);

/**
 * @brief Retrieve indicator blink state
 * @details Gets current LED blink state for sampling indication
 * @param None
 * @return uint8_t Current blink state (0 or 1)
 */
uint8_t RetrieveIndicatorBlinkState(void);

/**
 * @brief Handle data storage operations
 * @details Manages storage of sample and overlimit data
 * @param voltage Voltage value to store
 * @param timestamp Sample timestamp
 * @param isOverlimit Flag indicating if this is an overlimit sample
 * @return DataAcquisitionStatus Storage operation result
 */
DataAcquisitionStatus HandleDataStorageOperations(
  float voltage, 
  uint32_t timestamp, 
  uint8_t isOverlimit
);

/**
 * @brief Get acquisition statistics
 * @details Retrieves sampling statistics and counters
 * @param totalSamples Pointer to store total sample count
 * @param overlimitEvents Pointer to store overlimit event count
 * @return DataAcquisitionStatus Operation result
 */
DataAcquisitionStatus GetAcquisitionStatistics(
  uint32_t* totalSamples, 
  uint32_t* overlimitEvents
);

/**
 * @brief Set voltage threshold
 * @details Configures the overlimit detection threshold
 * @param threshold New threshold value in volts
 * @return DataAcquisitionStatus Configuration result
 */
DataAcquisitionStatus SetVoltageThreshold(float threshold);

/**
 * @brief Get data acquisition context
 * @details Provides access to acquisition state information
 * @param None
 * @return DataAcquisitionContext* Pointer to acquisition context
 */
DataAcquisitionContext* GetDataAcquisitionContext(void);

#endif /* DATA_ACQUISITION_MANAGER_H */