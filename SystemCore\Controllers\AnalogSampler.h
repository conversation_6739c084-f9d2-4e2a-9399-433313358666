/**
 * @file AnalogSampler.h
 * @brief Analog signal sampling controller interface
 * <AUTHOR> Systems Team
 * @date 2025-01-15
 * @copyright Copyright (c) 2025 Advanced Embedded Solutions. All rights reserved.
 */

#ifndef ANALOG_SAMPLER_H
#define ANALOG_SAMPLER_H

#include "SystemDefinitions.h"

/* Analog Sampling Configuration */
#define ANALOG_CONVERSION_TIMEOUT_MS     1000
#define ANALOG_REFERENCE_VOLTAGE         3.3f
#define ANALOG_RESOLUTION_BITS           12
#define ANALOG_MAX_DIGITAL_VALUE         4095

/* Sampling Mode Definitions */
#define SAMPLING_MODE_POLLING            0
#define SAMPLING_MODE_DMA                1
#define SAMPLING_MODE_TIMER_DMA          2

/* Buffer Management */
#define ANALOG_SAMPLE_BUFFER_SIZE        SAMPLE_BUFFER_CAPACITY
#define ANALOG_DMA_TRANSFER_SIZE         ANALOG_DMA_BUFFER_SIZE

/**
 * @brief Analog sampling context structure
 * @details Contains all state information for analog sampling operations
 */
typedef struct AnalogSamplingContext
{
  uint32_t m_analogDataBuffer[ANALOG_SAMPLE_BUFFER_SIZE];  /**< Main data buffer */
  uint32_t m_dacValueBuffer[ANALOG_DMA_TRANSFER_SIZE];     /**< DAC output buffer */
  uint32_t m_resultValueBuffer[ANALOG_DMA_TRANSFER_SIZE];  /**< Processing results */
  uint32_t m_analogValue;                                  /**< Current analog reading */
  float m_currentVoltageValue;                             /**< Converted voltage value */
  uint8_t m_analogConversionComplete;                      /**< Conversion completion flag */
  uint8_t m_waveformAnalysisFlag;                          /**< Waveform analysis enable */
  uint8_t m_waveformQueryType;                             /**< Type of waveform query */
  uint8_t m_currentSamplingMode;                           /**< Active sampling mode */
} AnalogSamplingContext;

/* Public Function Declarations */

/**
 * @brief Initialize analog sampling system
 * @details Sets up ADC, DMA, and sampling buffers
 * @param None
 * @return DataAcquisitionStatus Initialization result
 */
DataAcquisitionStatus InitializeAnalogSampler(void);

/**
 * @brief Process analog sampling operations
 * @details Main task function for analog data acquisition
 * @param None
 * @return None
 * @note This function should be called periodically by the task scheduler
 */
void ProcessAnalogSampling(void);

/**
 * @brief Initialize DMA timer-based sampling
 * @details Configures timer-triggered DMA sampling mode
 * @param None
 * @return DataAcquisitionStatus Configuration result
 */
DataAcquisitionStatus InitializeDmaTimerSampling(void);

/**
 * @brief Initialize DMA analog sampling
 * @details Configures DMA-based continuous sampling
 * @param None
 * @return DataAcquisitionStatus Configuration result
 */
DataAcquisitionStatus InitializeDmaAnalogSampling(void);

/**
 * @brief Analog conversion complete callback
 * @details Handles ADC conversion completion events
 * @param hadc Pointer to ADC handle
 * @return None
 * @note This is a HAL callback function
 */
void OnAnalogConversionComplete(ADC_HandleTypeDef* hadc);

/**
 * @brief Get current voltage reading
 * @details Returns the most recent voltage measurement
 * @param None
 * @return float Current voltage value in volts
 */
float GetCurrentVoltageReading(void);

/**
 * @brief Get analog sampling context
 * @details Provides access to sampling state information
 * @param None
 * @return AnalogSamplingContext* Pointer to sampling context
 */
AnalogSamplingContext* GetAnalogSamplingContext(void);

/**
 * @brief Set sampling mode
 * @details Configures the analog sampling operation mode
 * @param mode Sampling mode (polling, DMA, or timer-DMA)
 * @return DataAcquisitionStatus Configuration result
 */
DataAcquisitionStatus SetAnalogSamplingMode(uint8_t mode);

/**
 * @brief Start analog sampling
 * @details Begins continuous analog data acquisition
 * @param None
 * @return DataAcquisitionStatus Start operation result
 */
DataAcquisitionStatus StartAnalogSampling(void);

/**
 * @brief Stop analog sampling
 * @details Halts analog data acquisition
 * @param None
 * @return DataAcquisitionStatus Stop operation result
 */
DataAcquisitionStatus StopAnalogSampling(void);

/**
 * @brief Convert digital value to voltage
 * @details Converts ADC digital reading to voltage value
 * @param digitalValue Raw ADC reading
 * @return float Converted voltage value
 */
float ConvertDigitalToVoltage(uint32_t digitalValue);

/**
 * @brief Enable waveform analysis
 * @details Activates advanced waveform processing
 * @param analysisType Type of analysis to perform
 * @return DataAcquisitionStatus Enable operation result
 */
DataAcquisitionStatus EnableWaveformAnalysis(uint8_t analysisType);

/**
 * @brief Disable waveform analysis
 * @details Deactivates waveform processing
 * @param None
 * @return DataAcquisitionStatus Disable operation result
 */
DataAcquisitionStatus DisableWaveformAnalysis(void);

#endif /* ANALOG_SAMPLER_H */