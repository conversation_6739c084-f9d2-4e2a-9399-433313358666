/**
 * @file PersistentStorage.h
 * @brief Persistent data storage management interface
 * <AUTHOR> Systems Team
 * @date 2025-01-15
 * @copyright Copyright (c) 2025 Advanced Embedded Solutions. All rights reserved.
 */

#ifndef PERSISTENT_STORAGE_H
#define PERSISTENT_STORAGE_H

#include "SystemDefinitions.h"

/* Storage Configuration Constants */
#define STORAGE_FILENAME_MAX_LENGTH      64
#define STORAGE_DATETIME_STRING_LENGTH   32
#define STORAGE_DIRECTORY_MAX_LENGTH     32

/**
 * @brief Storage file state structure
 * @details Tracks the state and metadata of storage files
 */
typedef struct StorageFileState
{
  char currentFilename[STORAGE_FILENAME_MAX_LENGTH];  /**< Current active filename */
  char directoryPath[STORAGE_DIRECTORY_MAX_LENGTH];   /**< Storage directory path */
  uint32_t fileSize;                                  /**< Current file size in bytes */
  uint32_t recordCount;                               /**< Number of records in file */
  uint8_t fileOpen;                                   /**< File open status flag */
  DataStorageCategory storageCategory;                /**< Type of data being stored */
} StorageFileState;

/* Public Function Declarations */

/**
 * @brief Initialize the persistent storage system
 * @details Sets up storage directories and file management
 * @param None
 * @return PersistentStorageStatus Initialization result
 */
PersistentStorageStatus InitializePersistentStorage(void);

/**
 * @brief Store sample data to persistent storage
 * @details Writes sampling data with timestamp to storage
 * @param voltage Voltage measurement value
 * @param timestamp Data collection timestamp
 * @return PersistentStorageStatus Storage operation result
 */
PersistentStorageStatus StoreSampleData(float voltage, uint32_t timestamp);

/**
 * @brief Store overlimit data to persistent storage
 * @details Records voltage readings that exceed configured limits
 * @param voltage Overlimit voltage value
 * @param timestamp Detection timestamp
 * @return PersistentStorageStatus Storage operation result
 */
PersistentStorageStatus StoreOverlimitData(float voltage, uint32_t timestamp);

/**
 * @brief Record system log entry
 * @details Stores system events and diagnostic information
 * @param logMessage Log message string
 * @param logLevel Log severity level
 * @return PersistentStorageStatus Storage operation result
 */
PersistentStorageStatus RecordSystemLog(const char* logMessage, uint8_t logLevel);

/**
 * @brief Store hidden data to persistent storage
 * @details Stores concealed data for special operations
 * @param dataBuffer Pointer to data buffer
 * @param dataLength Length of data to store
 * @return PersistentStorageStatus Storage operation result
 */
PersistentStorageStatus StoreHiddenData(const uint8_t* dataBuffer, uint32_t dataLength);

/**
 * @brief Generate datetime string for file naming
 * @details Creates formatted datetime string for file identification
 * @param timestamp Unix timestamp
 * @param dateTimeString Output buffer for datetime string
 * @param bufferSize Size of output buffer
 * @return PersistentStorageStatus Generation result
 */
PersistentStorageStatus GenerateDateTimeString(
  uint32_t timestamp, 
  char* dateTimeString, 
  uint32_t bufferSize
);

/**
 * @brief Generate storage filename
 * @details Creates appropriate filename based on storage category
 * @param category Data storage category
 * @param timestamp File creation timestamp
 * @param filename Output buffer for filename
 * @param bufferSize Size of output buffer
 * @return PersistentStorageStatus Generation result
 */
PersistentStorageStatus GenerateStorageFilename(
  DataStorageCategory category,
  uint32_t timestamp,
  char* filename,
  uint32_t bufferSize
);

/**
 * @brief Test persistent storage functionality
 * @details Performs comprehensive storage system testing
 * @param None
 * @return PersistentStorageStatus Test result
 */
PersistentStorageStatus TestPersistentStorage(void);

/**
 * @brief Get storage file state information
 * @details Retrieves current state of storage files
 * @param category Storage category to query
 * @param fileState Pointer to file state structure to fill
 * @return PersistentStorageStatus Query result
 */
PersistentStorageStatus GetStorageFileState(
  DataStorageCategory category,
  StorageFileState* fileState
);

/**
 * @brief Close all open storage files
 * @details Safely closes all active storage files
 * @param None
 * @return PersistentStorageStatus Close operation result
 */
PersistentStorageStatus CloseAllStorageFiles(void);

#endif /* PERSISTENT_STORAGE_H */