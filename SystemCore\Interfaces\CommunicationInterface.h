/**
 * @file CommunicationInterface.h
 * @brief Serial communication and command processing interface
 * <AUTHOR> Systems Team
 * @date 2025-01-15
 * @copyright Copyright (c) 2025 Advanced Embedded Solutions. All rights reserved.
 */

#ifndef COMMUNICATION_INTERFACE_H
#define COMMUNICATION_INTERFACE_H

#include "SystemDefinitions.h"
#include "rt_ringbuffer.h"

/* Communication Configuration */
#define COMMUNICATION_BUFFER_SIZE        512
#define COMMUNICATION_DMA_BUFFER_SIZE    256
#define COMMUNICATION_COMMAND_MAX_LENGTH 64
#define COMMUNICATION_RESPONSE_MAX_LENGTH 256
#define COMMUNICATION_TIMEOUT_MS         1000

/* Command Processing States */
#define COMMAND_PROCESSING_COMPLETE      0
#define COMMAND_PROCESSING_PENDING       1
#define COMMAND_PROCESSING_ERROR         2

/**
 * @brief Communication context structure
 * @details Contains all state information for communication operations
 */
typedef struct CommunicationContext
{
  uint8_t m_receiveDataBuffer[COMMUNICATION_BUFFER_SIZE];     /**< Main receive buffer */
  uint8_t m_receiveDmaBuffer[COMMUNICATION_DMA_BUFFER_SIZE];  /**< DMA receive buffer */
  uint8_t m_transmitDmaBuffer[COMMUNICATION_DMA_BUFFER_SIZE]; /**< DMA transmit buffer */
  uint16_t m_receiveBufferIndex;                              /**< Receive buffer index */
  uint32_t m_receiveTimestamp;                                /**< Last receive timestamp */
  uint8_t m_communicationFlag;                                /**< Communication status flag */
  rt_ringbuffer m_communicationRingBuffer;                   /**< Ring buffer for data */
  uint8_t m_ringBufferMemoryPool[COMMUNICATION_BUFFER_SIZE];  /**< Ring buffer memory */
  CommandProcessingState m_commandProcessingState;           /**< Command processing state */
  uint8_t m_samplingOutputEnabled;                            /**< Sampling output enable flag */
  uint32_t m_lastOutputTimestamp;                             /**< Last output timestamp */
  DataOutputFormat m_dataOutputFormat;                       /**< Data output format */
} CommunicationContext;

/**
 * @brief Command handler function type
 * @param commandArgs Command arguments string
 * @param responseBuffer Buffer for command response
 * @param responseBufferSize Size of response buffer
 * @return uint8_t Command processing result
 */
typedef uint8_t (*CommandHandler)(const char* commandArgs, char* responseBuffer, uint32_t responseBufferSize);

/**
 * @brief Command definition structure
 * @details Defines a command and its handler function
 */
typedef struct CommandDefinition
{
  const char* commandName;                     /**< Command name string */
  CommandHandler handlerFunction;              /**< Command handler function */
  const char* commandDescription;              /**< Command description */
  uint8_t requiresParameters;                  /**< 1 if command requires parameters */
} CommandDefinition;

/* Public Function Declarations */

/**
 * @brief Initialize communication interface
 * @details Sets up UART, DMA, and command processing
 * @param None
 * @return DataAcquisitionStatus Initialization result
 */
DataAcquisitionStatus InitializeCommunicationInterface(void);

/**
 * @brief Process communication operations
 * @details Main task function for communication handling
 * @param None
 * @return None
 * @note This function should be called periodically by the task scheduler
 */
void ProcessCommunication(void);

/**
 * @brief Parse incoming command
 * @details Processes received command string and executes appropriate handler
 * @param commandString Received command string
 * @return uint8_t Command processing result
 */
uint8_t ParseIncomingCommand(const char* commandString);

/**
 * @brief Transmit formatted data
 * @details Sends formatted string via UART
 * @param format Printf-style format string
 * @param ... Variable arguments for formatting
 * @return uint32_t Number of bytes transmitted
 */
uint32_t TransmitFormattedData(const char* format, ...);

/**
 * @brief Process configuration command
 * @details Handles configuration-related commands
 * @param commandArgs Command arguments
 * @param responseBuffer Response buffer
 * @param responseBufferSize Response buffer size
 * @return uint8_t Processing result
 */
uint8_t ProcessConfigurationCommand(const char* commandArgs, char* responseBuffer, uint32_t responseBufferSize);

/**
 * @brief Process ratio command
 * @details Handles voltage ratio configuration commands
 * @param commandArgs Command arguments
 * @param responseBuffer Response buffer
 * @param responseBufferSize Response buffer size
 * @return uint8_t Processing result
 */
uint8_t ProcessRatioCommand(const char* commandArgs, char* responseBuffer, uint32_t responseBufferSize);

/**
 * @brief Process limit command
 * @details Handles voltage limit configuration commands
 * @param commandArgs Command arguments
 * @param responseBuffer Response buffer
 * @param responseBufferSize Response buffer size
 * @return uint8_t Processing result
 */
uint8_t ProcessLimitCommand(const char* commandArgs, char* responseBuffer, uint32_t responseBufferSize);

/**
 * @brief Process start command
 * @details Handles sampling start commands
 * @param commandArgs Command arguments
 * @param responseBuffer Response buffer
 * @param responseBufferSize Response buffer size
 * @return uint8_t Processing result
 */
uint8_t ProcessStartCommand(const char* commandArgs, char* responseBuffer, uint32_t responseBufferSize);

/**
 * @brief Process stop command
 * @details Handles sampling stop commands
 * @param commandArgs Command arguments
 * @param responseBuffer Response buffer
 * @param responseBufferSize Response buffer size
 * @return uint8_t Processing result
 */
uint8_t ProcessStopCommand(const char* commandArgs, char* responseBuffer, uint32_t responseBufferSize);

/**
 * @brief Process hide command
 * @details Handles data hiding commands
 * @param commandArgs Command arguments
 * @param responseBuffer Response buffer
 * @param responseBufferSize Response buffer size
 * @return uint8_t Processing result
 */
uint8_t ProcessHideCommand(const char* commandArgs, char* responseBuffer, uint32_t responseBufferSize);

/**
 * @brief Process unhide command
 * @details Handles data unhiding commands
 * @param commandArgs Command arguments
 * @param responseBuffer Response buffer
 * @param responseBufferSize Response buffer size
 * @return uint8_t Processing result
 */
uint8_t ProcessUnhideCommand(const char* commandArgs, char* responseBuffer, uint32_t responseBufferSize);

/**
 * @brief Format hexadecimal output
 * @details Formats data as hexadecimal string
 * @param data Pointer to data to format
 * @param dataLength Length of data in bytes
 * @param outputBuffer Output buffer for formatted string
 * @param outputBufferSize Size of output buffer
 * @return uint32_t Number of characters written
 */
uint32_t FormatHexadecimalOutput(const uint8_t* data, uint32_t dataLength, char* outputBuffer, uint32_t outputBufferSize);

/**
 * @brief Convert RTC to Unix timestamp
 * @details Converts RTC time structure to Unix timestamp
 * @param rtcTime Pointer to RTC time structure
 * @return uint32_t Unix timestamp
 */
uint32_t ConvertRtcToUnixTimestamp(const RTC_TimeTypeDef* rtcTime);

/**
 * @brief Enable sampling output
 * @details Enables automatic sampling data output
 * @param enable 1 to enable, 0 to disable
 * @return DataAcquisitionStatus Operation result
 */
DataAcquisitionStatus EnableSamplingOutput(uint8_t enable);

/**
 * @brief Set data output format
 * @details Configures the format for data output
 * @param format Output format type
 * @return DataAcquisitionStatus Configuration result
 */
DataAcquisitionStatus SetDataOutputFormat(DataOutputFormat format);

/**
 * @brief Get communication context
 * @details Provides access to communication state information
 * @param None
 * @return CommunicationContext* Pointer to communication context
 */
CommunicationContext* GetCommunicationContext(void);

/**
 * @brief Send response message
 * @details Transmits response message via communication interface
 * @param message Response message string
 * @return DataAcquisitionStatus Transmission result
 */
DataAcquisitionStatus SendResponseMessage(const char* message);

/**
 * @brief Register command handler
 * @details Adds a new command to the command processing system
 * @param commandDef Pointer to command definition structure
 * @return DataAcquisitionStatus Registration result
 */
DataAcquisitionStatus RegisterCommandHandler(const CommandDefinition* commandDef);

#endif /* COMMUNICATION_INTERFACE_H */