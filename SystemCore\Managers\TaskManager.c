/**
 * @file TaskManager.c
 * @brief System task scheduler management implementation
 * <AUTHOR> Systems Team
 * @date 2025-01-15
 * @copyright Copyright (c) 2025 Advanced Embedded Solutions. All rights reserved.
 * 
 * This module implements comprehensive task scheduling capabilities including
 * task registration, execution control, and priority management.
 */

#include "TaskManager.h"

/* Private Variables */
static SystemTaskDescriptor g_systemTaskArray[MAXIMUM_SYSTEM_TASKS];
static uint8_t m_totalTaskCount = 0;
static uint32_t m_schedulerStartTime = 0;

/* Private Function Prototypes */
static uint8_t IsTaskReadyForExecution(const SystemTaskDescriptor* task);
static void UpdateTaskExecutionTime(SystemTaskDescriptor* task);
static SystemConfigurationStatus ValidateTaskParameters(
  SystemTaskFunction taskFunction,
  uint32_t intervalMs,
  const char* taskName
);

/**
 * @brief Initialize the system task scheduler
 */
SystemConfigurationStatus InitializeSystemScheduler(void)
{
  /* Clear task array */
  ZERO_MEMORY(g_systemTaskArray, sizeof(g_systemTaskArray));
  
  /* Reset task counter */
  m_totalTaskCount = 0;
  
  /* Record scheduler start time */
  m_schedulerStartTime = HAL_GetTick();
  
  INFO_PRINT("Task scheduler initialized successfully");
  
  return CONFIGURATION_SUCCESS;
}

/**
 * @brief Execute the task scheduler main loop
 */
void ExecuteTaskScheduler(void)
{
  uint8_t taskIndex;
  SystemTaskDescriptor* currentTask;
  
  /* Iterate through all registered tasks */
  for (taskIndex = 0; taskIndex < m_totalTaskCount; taskIndex++)
  {
    currentTask = &g_systemTaskArray[taskIndex];
    
    /* Skip inactive or suspended tasks */
    if (currentTask->taskState != TASK_STATE_ACTIVE)
    {
      continue;
    }
    
    /* Check if task is ready for execution */
    if (IsTaskReadyForExecution(currentTask))
    {
      /* Execute the task function */
      if (currentTask->taskFunction != NULL)
      {
        currentTask->taskFunction();
        
        /* Update execution timestamp */
        UpdateTaskExecutionTime(currentTask);
        
        DEBUG_PRINT("Executed task: %s", currentTask->taskName);
      }
    }
  }
}

/**
 * @brief Register a new task with the scheduler
 */
SystemConfigurationStatus RegisterSystemTask(
  SystemTaskFunction taskFunction,
  uint32_t intervalMs,
  uint8_t priority,
  const char* taskName
)
{
  SystemTaskDescriptor* newTask;
  
  /* Validate input parameters */
  if (ValidateTaskParameters(taskFunction, intervalMs, taskName) != CONFIGURATION_SUCCESS)
  {
    ERROR_PRINT("Invalid task parameters for: %s", taskName ? taskName : "Unknown");
    return CONFIGURATION_FAILURE;
  }
  
  /* Check if task list is full */
  if (m_totalTaskCount >= MAXIMUM_SYSTEM_TASKS)
  {
    ERROR_PRINT("Task list is full, cannot register: %s", taskName);
    return CONFIGURATION_FAILURE;
  }
  
  /* Get pointer to new task slot */
  newTask = &g_systemTaskArray[m_totalTaskCount];
  
  /* Initialize task descriptor */
  newTask->taskFunction = taskFunction;
  newTask->executionInterval = intervalMs;
  newTask->lastExecutionTime = HAL_GetTick();
  newTask->taskState = TASK_STATE_ACTIVE;
  newTask->taskPriority = priority;
  newTask->taskName = taskName;
  
  /* Increment task counter */
  m_totalTaskCount++;
  
  INFO_PRINT("Registered task: %s (interval: %lu ms)", taskName, intervalMs);
  
  return CONFIGURATION_SUCCESS;
}

/* Additional function implementations would continue here... */
/* This is a framework - full implementation will be added during actual refactoring */