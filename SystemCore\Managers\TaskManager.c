/**
 * @file TaskManager.c
 * @brief System task scheduler management implementation
 * <AUTHOR> Systems Team
 * @date 2025-01-15
 * @copyright Copyright (c) 2025 Advanced Embedded Solutions. All rights reserved.
 * 
 * This module implements comprehensive task scheduling capabilities including
 * task registration, execution control, and priority management for embedded systems.
 */

#include "TaskManager.h"

/* External Task Function Declarations */
extern void UpdateStatusIndicators(void);      /* LED control task */
extern void ProcessAnalogSampling(void);       /* ADC sampling task */
extern void ProcessInputEvents(void);          /* Button handling task */
extern void ProcessCommunication(void);        /* UART communication task */
extern void oled_task(void);                   /* OLED display task */
extern void ExecuteSamplingTask(void);         /* Sampling control task */

/* Private Variables */
static uint8_t m_totalTaskCount = 0;
static uint32_t m_schedulerStartTime = 0;
static uint8_t m_schedulerInitialized = 0;

/**
 * @brief System task array with predefined task configurations
 * @details Contains all system tasks with their execution intervals and priorities
 */
static SystemTaskDescriptor g_systemTaskArray[] = 
{
  {
    .taskFunction = UpdateStatusIndicators,
    .executionInterval = 1,
    .lastExecutionTime = 0,
    .taskState = TASK_STATE_ACTIVE,
    .taskPriority = TASK_PRIORITY_HIGH,
    .taskName = "StatusIndicator"
  },
  {
    .taskFunction = ProcessAnalogSampling,
    .executionInterval = 100,
    .lastExecutionTime = 0,
    .taskState = TASK_STATE_ACTIVE,
    .taskPriority = TASK_PRIORITY_NORMAL,
    .taskName = "AnalogSampler"
  },
  {
    .taskFunction = ProcessInputEvents,
    .executionInterval = 5,
    .lastExecutionTime = 0,
    .taskState = TASK_STATE_ACTIVE,
    .taskPriority = TASK_PRIORITY_HIGH,
    .taskName = "InputHandler"
  },
  {
    .taskFunction = ProcessCommunication,
    .executionInterval = 5,
    .lastExecutionTime = 0,
    .taskState = TASK_STATE_ACTIVE,
    .taskPriority = TASK_PRIORITY_NORMAL,
    .taskName = "Communication"
  },
  {
    .taskFunction = oled_task,
    .executionInterval = 1,
    .lastExecutionTime = 0,
    .taskState = TASK_STATE_ACTIVE,
    .taskPriority = TASK_PRIORITY_LOW,
    .taskName = "OledDisplay"
  },
  {
    .taskFunction = ExecuteSamplingTask,
    .executionInterval = 10,
    .lastExecutionTime = 0,
    .taskState = TASK_STATE_ACTIVE,
    .taskPriority = TASK_PRIORITY_HIGH,
    .taskName = "SamplingControl"
  }
};

/* Private Function Prototypes */
static uint8_t IsTaskReadyForExecution(const SystemTaskDescriptor* taskDescriptor);
static void UpdateTaskExecutionTime(SystemTaskDescriptor* taskDescriptor);
static SystemConfigurationStatus ValidateTaskParameters(
  SystemTaskFunction taskFunction,
  uint32_t intervalMs,
  const char* taskName
);
static void SortTasksByPriority(void);

/**
 * @brief Initialize the system task scheduler
 * @details Sets up the task management system and prepares for task execution
 * @param None
 * @return SystemConfigurationStatus Initialization result
 * @retval CONFIGURATION_SUCCESS Initialization completed successfully
 * @retval CONFIGURATION_FAILURE Initialization failed
 * @note This function must be called before any task operations
 */
SystemConfigurationStatus InitializeSystemScheduler(void)
{
  uint8_t taskIndex;
  uint32_t currentTime;
  
  /* Get current system time */
  currentTime = HAL_GetTick();
  
  /* Calculate total number of predefined tasks */
  m_totalTaskCount = ARRAY_SIZE(g_systemTaskArray);
  
  /* Initialize all task execution times */
  for (taskIndex = 0; taskIndex < m_totalTaskCount; taskIndex++)
  {
    g_systemTaskArray[taskIndex].lastExecutionTime = currentTime;
    
    /* Validate task configuration */
    if (g_systemTaskArray[taskIndex].taskFunction == NULL)
    {
      ERROR_PRINT("Task %d has NULL function pointer", taskIndex);
      return CONFIGURATION_FAILURE;
    }
    
    DEBUG_PRINT("Initialized task: %s (interval: %lu ms, priority: %d)",
                g_systemTaskArray[taskIndex].taskName,
                g_systemTaskArray[taskIndex].executionInterval,
                g_systemTaskArray[taskIndex].taskPriority);
  }
  
  /* Sort tasks by priority for optimal execution order */
  SortTasksByPriority();
  
  /* Record scheduler initialization time */
  m_schedulerStartTime = currentTime;
  m_schedulerInitialized = 1;
  
  INFO_PRINT("Task scheduler initialized with %d tasks", m_totalTaskCount);
  
  return CONFIGURATION_SUCCESS;
}

/**
 * @brief Execute the task scheduler main loop
 * @details Processes all registered tasks according to their schedules
 * @param None
 * @return None
 * @note This function should be called continuously in the main loop
 */
void ExecuteTaskScheduler(void)
{
  uint8_t taskIndex;
  SystemTaskDescriptor* currentTask;
  uint32_t executionStartTime;
  
  if (!m_schedulerInitialized)
  {
    ERROR_PRINT("Task scheduler not initialized");
    return;
  }
  
  /* Iterate through all registered tasks */
  for (taskIndex = 0; taskIndex < m_totalTaskCount; taskIndex++)
  {
    currentTask = &g_systemTaskArray[taskIndex];
    
    /* Skip inactive or suspended tasks */
    if (currentTask->taskState != TASK_STATE_ACTIVE)
    {
      continue;
    }
    
    /* Check if task is ready for execution */
    if (IsTaskReadyForExecution(currentTask))
    {
      /* Record execution start time for performance monitoring */
      executionStartTime = HAL_GetTick();
      
      /* Execute the task function */
      if (currentTask->taskFunction != NULL)
      {
        currentTask->taskFunction();
        
        /* Update execution timestamp */
        UpdateTaskExecutionTime(currentTask);
        
        DEBUG_PRINT("Executed task: %s (duration: %lu ms)", 
                    currentTask->taskName,
                    HAL_GetTick() - executionStartTime);
      }
      else
      {
        ERROR_PRINT("Task %s has NULL function pointer", currentTask->taskName);
      }
    }
  }
}

/**
 * @brief Check if task is ready for execution
 * @details Determines if sufficient time has elapsed since last execution
 * @param taskDescriptor Pointer to task descriptor
 * @return uint8_t 1 if ready for execution, 0 otherwise
 */
static uint8_t IsTaskReadyForExecution(const SystemTaskDescriptor* taskDescriptor)
{
  uint32_t currentTime;
  uint32_t elapsedTime;
  
  if (taskDescriptor == NULL)
  {
    return 0;
  }
  
  currentTime = HAL_GetTick();
  
  /* Handle timer overflow case */
  if (currentTime >= taskDescriptor->lastExecutionTime)
  {
    elapsedTime = currentTime - taskDescriptor->lastExecutionTime;
  }
  else
  {
    /* Timer overflow occurred */
    elapsedTime = (UINT32_MAX - taskDescriptor->lastExecutionTime) + currentTime + 1;
  }
  
  /* Check if execution interval has elapsed */
  return (elapsedTime >= taskDescriptor->executionInterval) ? 1 : 0;
}

/**
 * @brief Update task execution timestamp
 * @details Records the current time as the last execution time
 * @param taskDescriptor Pointer to task descriptor
 * @return None
 */
static void UpdateTaskExecutionTime(SystemTaskDescriptor* taskDescriptor)
{
  if (taskDescriptor != NULL)
  {
    taskDescriptor->lastExecutionTime = HAL_GetTick();
  }
}

/**
 * @brief Sort tasks by priority for optimal execution order
 * @details Arranges tasks so higher priority tasks are executed first
 * @param None
 * @return None
 */
static void SortTasksByPriority(void)
{
  uint8_t i, j;
  SystemTaskDescriptor tempTask;
  
  /* Simple bubble sort by priority (higher priority = lower number) */
  for (i = 0; i < m_totalTaskCount - 1; i++)
  {
    for (j = 0; j < m_totalTaskCount - i - 1; j++)
    {
      if (g_systemTaskArray[j].taskPriority > g_systemTaskArray[j + 1].taskPriority)
      {
        /* Swap tasks */
        tempTask = g_systemTaskArray[j];
        g_systemTaskArray[j] = g_systemTaskArray[j + 1];
        g_systemTaskArray[j + 1] = tempTask;
      }
    }
  }
  
  DEBUG_PRINT("Tasks sorted by priority");
}

/**
 * @brief Register a new task with the scheduler
 * @details Adds a task to the system task list with specified parameters
 * @param taskFunction Pointer to the task execution function
 * @param intervalMs Task execution interval in milliseconds
 * @param priority Task priority level
 * @param taskName Human-readable task name
 * @return SystemConfigurationStatus Registration result
 * @retval CONFIGURATION_SUCCESS Task registered successfully
 * @retval CONFIGURATION_FAILURE Registration failed (task list full)
 */
SystemConfigurationStatus RegisterSystemTask(
  SystemTaskFunction taskFunction,
  uint32_t intervalMs,
  uint8_t priority,
  const char* taskName
)
{
  SystemTaskDescriptor* newTask;
  
  /* Validate input parameters */
  if (ValidateTaskParameters(taskFunction, intervalMs, taskName) != CONFIGURATION_SUCCESS)
  {
    ERROR_PRINT("Invalid task parameters for: %s", taskName ? taskName : "Unknown");
    return CONFIGURATION_FAILURE;
  }
  
  /* Check if task list is full */
  if (m_totalTaskCount >= MAXIMUM_SYSTEM_TASKS)
  {
    ERROR_PRINT("Task list is full, cannot register: %s", taskName);
    return CONFIGURATION_FAILURE;
  }
  
  /* Get pointer to new task slot */
  newTask = &g_systemTaskArray[m_totalTaskCount];
  
  /* Initialize task descriptor */
  newTask->taskFunction = taskFunction;
  newTask->executionInterval = intervalMs;
  newTask->lastExecutionTime = HAL_GetTick();
  newTask->taskState = TASK_STATE_ACTIVE;
  newTask->taskPriority = priority;
  newTask->taskName = taskName;
  
  /* Increment task counter */
  m_totalTaskCount++;
  
  /* Re-sort tasks by priority */
  SortTasksByPriority();
  
  INFO_PRINT("Registered task: %s (interval: %lu ms, priority: %d)", 
             taskName, intervalMs, priority);
  
  return CONFIGURATION_SUCCESS;
}

/**
 * @brief Validate task parameters
 * @details Checks if task parameters are valid
 * @param taskFunction Task function pointer
 * @param intervalMs Execution interval
 * @param taskName Task name
 * @return SystemConfigurationStatus Validation result
 */
static SystemConfigurationStatus ValidateTaskParameters(
  SystemTaskFunction taskFunction,
  uint32_t intervalMs,
  const char* taskName
)
{
  if (taskFunction == NULL)
  {
    ERROR_PRINT("Task function pointer is NULL");
    return CONFIGURATION_FAILURE;
  }
  
  if (intervalMs == 0)
  {
    ERROR_PRINT("Task interval cannot be zero");
    return CONFIGURATION_FAILURE;
  }
  
  if (taskName == NULL)
  {
    ERROR_PRINT("Task name is NULL");
    return CONFIGURATION_FAILURE;
  }
  
  return CONFIGURATION_SUCCESS;
}

/**
 * @brief Get current system tick count
 * @details Returns the current system timestamp for timing calculations
 * @param None
 * @return uint32_t Current system tick count in milliseconds
 */
uint32_t GetSystemTickCount(void)
{
  return HAL_GetTick();
}

/**
 * @brief Get total number of registered tasks
 * @details Returns the count of currently registered tasks
 * @param None
 * @return uint8_t Number of registered tasks
 */
uint8_t GetRegisteredTaskCount(void)
{
  return m_totalTaskCount;
}

/**
 * @brief Get task information by index
 * @details Retrieves task descriptor for a specific task
 * @param taskIndex Index of the task to query
 * @return SystemTaskDescriptor* Pointer to task descriptor or NULL if invalid
 */
SystemTaskDescriptor* GetTaskDescriptor(uint8_t taskIndex)
{
  if (taskIndex >= m_totalTaskCount)
  {
    ERROR_PRINT("Invalid task index: %d", taskIndex);
    return NULL;
  }
  
  return &g_systemTaskArray[taskIndex];
}

/**
 * @brief Suspend a specific task
 * @details Temporarily stops task execution without removing it
 * @param taskIndex Index of the task to suspend
 * @return SystemConfigurationStatus Operation result
 */
SystemConfigurationStatus SuspendSystemTask(uint8_t taskIndex)
{
  if (taskIndex >= m_totalTaskCount)
  {
    ERROR_PRINT("Invalid task index: %d", taskIndex);
    return CONFIGURATION_FAILURE;
  }
  
  g_systemTaskArray[taskIndex].taskState = TASK_STATE_SUSPENDED;
  
  INFO_PRINT("Suspended task: %s", g_systemTaskArray[taskIndex].taskName);
  
  return CONFIGURATION_SUCCESS;
}

/**
 * @brief Resume a suspended task
 * @details Restarts execution of a previously suspended task
 * @param taskIndex Index of the task to resume
 * @return SystemConfigurationStatus Operation result
 */
SystemConfigurationStatus ResumeSystemTask(uint8_t taskIndex)
{
  if (taskIndex >= m_totalTaskCount)
  {
    ERROR_PRINT("Invalid task index: %d", taskIndex);
    return CONFIGURATION_FAILURE;
  }
  
  g_systemTaskArray[taskIndex].taskState = TASK_STATE_ACTIVE;
  g_systemTaskArray[taskIndex].lastExecutionTime = HAL_GetTick();
  
  INFO_PRINT("Resumed task: %s", g_systemTaskArray[taskIndex].taskName);
  
  return CONFIGURATION_SUCCESS;
}