/**
 * @file InputHandler.h
 * @brief User input handling and button management interface
 * <AUTHOR> Systems Team
 * @date 2025-01-15
 * @copyright Copyright (c) 2025 Advanced Embedded Solutions. All rights reserved.
 */

#ifndef INPUT_HANDLER_H
#define INPUT_HANDLER_H

#include "SystemDefinitions.h"
#include "ebtn.h"

/* Input Handler Configuration */
#define INPUT_HANDLER_BUTTON_COUNT       INPUT_DEVICE_BUTTON_MAX
#define INPUT_DEBOUNCE_TIME_MS           50
#define INPUT_LONG_PRESS_TIME_MS         2000
#define INPUT_DOUBLE_CLICK_TIME_MS       300

/* Button Event Types */
#define BUTTON_EVENT_NONE                0
#define BUTTON_EVENT_PRESS               1
#define BUTTON_EVENT_RELEASE             2
#define BUTTON_EVENT_LONG_PRESS          3
#define BUTTON_EVENT_DOUBLE_CLICK        4

/**
 * @brief Button configuration structure
 * @details Contains configuration for individual button behavior
 */
typedef struct ButtonConfiguration
{
  GPIO_TypeDef* gpioPort;              /**< GPIO port for button */
  uint16_t gpioPin;                    /**< GPIO pin for button */
  uint8_t activeLow;                   /**< 1 if button is active low */
  uint8_t enableLongPress;             /**< Enable long press detection */
  uint8_t enableDoubleClick;           /**< Enable double click detection */
  uint32_t longPressThreshold;         /**< Long press time threshold */
  uint32_t doubleClickThreshold;       /**< Double click time threshold */
} ButtonConfiguration;

/**
 * @brief Input event callback function type
 * @param buttonId Button identifier that generated the event
 * @param eventType Type of button event
 * @param eventData Additional event data (if any)
 */
typedef void (*InputEventCallback)(InputDeviceIdentifier buttonId, uint8_t eventType, uint32_t eventData);

/* Public Function Declarations */

/**
 * @brief Initialize input handler system
 * @details Sets up button GPIO and event handling
 * @param None
 * @return DataAcquisitionStatus Initialization result
 */
DataAcquisitionStatus InitializeInputHandler(void);

/**
 * @brief Process input events
 * @details Main task function for input processing
 * @param None
 * @return None
 * @note This function should be called periodically by the task scheduler
 */
void ProcessInputEvents(void);

/**
 * @brief Read button state
 * @details Gets current physical state of specified button
 * @param buttonId Button identifier to read
 * @return uint8_t Current button state (1 = pressed, 0 = released)
 */
uint8_t ReadButtonState(InputDeviceIdentifier buttonId);

/**
 * @brief Handle button event
 * @details Processes button events and triggers appropriate actions
 * @param buttonId Button that generated the event
 * @param eventType Type of event that occurred
 * @return None
 */
void HandleButtonEvent(InputDeviceIdentifier buttonId, uint8_t eventType);

/**
 * @brief Register input event callback
 * @details Sets up callback function for input events
 * @param callback Pointer to callback function
 * @return DataAcquisitionStatus Registration result
 */
DataAcquisitionStatus RegisterInputEventCallback(InputEventCallback callback);

/**
 * @brief Configure button parameters
 * @details Sets up button-specific configuration
 * @param buttonId Button identifier to configure
 * @param config Pointer to button configuration structure
 * @return DataAcquisitionStatus Configuration result
 */
DataAcquisitionStatus ConfigureButton(
  InputDeviceIdentifier buttonId, 
  const ButtonConfiguration* config
);

/**
 * @brief Enable button
 * @details Activates button event processing
 * @param buttonId Button identifier to enable
 * @return DataAcquisitionStatus Operation result
 */
DataAcquisitionStatus EnableButton(InputDeviceIdentifier buttonId);

/**
 * @brief Disable button
 * @details Deactivates button event processing
 * @param buttonId Button identifier to disable
 * @return DataAcquisitionStatus Operation result
 */
DataAcquisitionStatus DisableButton(InputDeviceIdentifier buttonId);

/**
 * @brief Get button press count
 * @details Retrieves total press count for specified button
 * @param buttonId Button identifier to query
 * @return uint32_t Total press count
 */
uint32_t GetButtonPressCount(InputDeviceIdentifier buttonId);

/**
 * @brief Reset button statistics
 * @details Clears press counters and statistics for specified button
 * @param buttonId Button identifier to reset
 * @return DataAcquisitionStatus Reset operation result
 */
DataAcquisitionStatus ResetButtonStatistics(InputDeviceIdentifier buttonId);

/**
 * @brief Test all buttons
 * @details Performs button functionality test
 * @param None
 * @return DataAcquisitionStatus Test result
 */
DataAcquisitionStatus TestAllButtons(void);

/* Button-specific action functions */

/**
 * @brief Handle sampling start/stop button
 * @details Processes button events for sampling control
 * @param eventType Type of button event
 * @return None
 */
void HandleSamplingControlButton(uint8_t eventType);

/**
 * @brief Handle sampling cycle change button
 * @details Processes button events for cycle adjustment
 * @param eventType Type of button event
 * @return None
 */
void HandleSamplingCycleButton(uint8_t eventType);

/**
 * @brief Handle configuration mode button
 * @details Processes button events for configuration access
 * @param eventType Type of button event
 * @return None
 */
void HandleConfigurationModeButton(uint8_t eventType);

/**
 * @brief Handle display mode button
 * @details Processes button events for display control
 * @param eventType Type of button event
 * @return None
 */
void HandleDisplayModeButton(uint8_t eventType);

/**
 * @brief Handle system reset button
 * @details Processes button events for system reset
 * @param eventType Type of button event
 * @return None
 */
void HandleSystemResetButton(uint8_t eventType);

/**
 * @brief Handle user function button
 * @details Processes button events for user-defined functions
 * @param buttonId User button identifier
 * @param eventType Type of button event
 * @return None
 */
void HandleUserFunctionButton(InputDeviceIdentifier buttonId, uint8_t eventType);

#endif /* INPUT_HANDLER_H */