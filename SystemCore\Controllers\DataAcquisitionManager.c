/**
 * @file DataAcquisitionManager.c
 * @brief Data acquisition and sampling control manager implementation
 * <AUTHOR> Systems Team
 * @date 2025-01-15
 * @copyright Copyright (c) 2025 Advanced Embedded Solutions. All rights reserved.
 */

#include "DataAcquisitionManager.h"
#include "AnalogSampler.h"
#include "StatusIndicator.h"
#include "PersistentStorage.h"

/* Private Variables */
static DataAcquisitionContext m_acquisitionContext;
static uint8_t m_acquisitionInitialized = 0;

/* Sampling interval conversion table (seconds to milliseconds) */
static const uint32_t m_samplingIntervalMs[] = 
{
  SAMPLING_INTERVAL_FIVE_SECONDS * 1000,      /* 5 seconds */
  SAMPLING_INTERVAL_TEN_SECONDS * 1000,       /* 10 seconds */
  SAMPLING_INTERVAL_FIFTEEN_SECONDS * 1000    /* 15 seconds */
};

/**
 * @brief Initialize data acquisition manager
 */
DataAcquisitionStatus InitializeDataAcquisition(void)
{
  /* Clear acquisition context */
  ZERO_MEMORY(&m_acquisitionContext, sizeof(DataAcquisitionContext));
  
  /* Set default configuration */
  m_acquisitionContext.acquisitionState = ACQUISITION_STATE_SYSTEM_IDLE;
  m_acquisitionContext.samplingInterval = SAMPLING_INTERVAL_TYPE_FIVE;
  m_acquisitionContext.voltageThreshold = ACQUISITION_VOLTAGE_THRESHOLD;
  m_acquisitionContext.overlimitCondition = 0;
  m_acquisitionContext.overlimitDebounceCounter = 0;
  m_acquisitionContext.totalSampleCount = 0;
  m_acquisitionContext.overlimitEventCount = 0;
  m_acquisitionContext.indicatorBlinkState = 0;
  
  /* Initialize timestamps */
  m_acquisitionContext.lastSampleTimestamp = HAL_GetTick();
  m_acquisitionContext.nextSampleTimestamp = m_acquisitionContext.lastSampleTimestamp + 
                                             m_samplingIntervalMs[m_acquisitionContext.samplingInterval];
  
  m_acquisitionInitialized = 1;
  
  INFO_PRINT("Data acquisition manager initialized");
  
  return DATA_ACQUISITION_SUCCESS;
}

/**
 * @brief Begin data collection operations
 */
DataAcquisitionStatus BeginDataCollection(void)
{
  if (!m_acquisitionInitialized)
  {
    ERROR_PRINT("Data acquisition not initialized");
    return DATA_ACQUISITION_FAILURE;
  }
  
  if (m_acquisitionContext.acquisitionState == ACQUISITION_STATE_SYSTEM_ACTIVE)
  {
    INFO_PRINT("Data collection already active");
    return DATA_ACQUISITION_SUCCESS;
  }
  
  /* Set acquisition state to active */
  m_acquisitionContext.acquisitionState = ACQUISITION_STATE_SYSTEM_ACTIVE;
  
  /* Reset timestamps */
  m_acquisitionContext.lastSampleTimestamp = HAL_GetTick();
  m_acquisitionContext.nextSampleTimestamp = m_acquisitionContext.lastSampleTimestamp + 
                                             m_samplingIntervalMs[m_acquisitionContext.samplingInterval];
  
  /* Update status indication */
  SetSamplingStatusIndication(1);
  
  INFO_PRINT("Data collection started with %d second interval", 
             (int)(m_samplingIntervalMs[m_acquisitionContext.samplingInterval] / 1000));
  
  return DATA_ACQUISITION_SUCCESS;
}

/**
 * @brief Terminate data collection operations
 */
DataAcquisitionStatus TerminateDataCollection(void)
{
  if (!m_acquisitionInitialized)
  {
    ERROR_PRINT("Data acquisition not initialized");
    return DATA_ACQUISITION_FAILURE;
  }
  
  /* Set acquisition state to idle */
  m_acquisitionContext.acquisitionState = ACQUISITION_STATE_SYSTEM_IDLE;
  
  /* Clear overlimit condition */
  m_acquisitionContext.overlimitCondition = 0;
  m_acquisitionContext.overlimitDebounceCounter = 0;
  
  /* Update status indication */
  SetSamplingStatusIndication(0);
  SetOverlimitStatusIndication(0);
  
  INFO_PRINT("Data collection stopped");
  
  return DATA_ACQUISITION_SUCCESS;
}

/**
 * @brief Check if sampling should be performed
 */
uint8_t ShouldPerformSampling(void)
{
  uint32_t currentTime;
  
  if (!m_acquisitionInitialized)
  {
    return 0;
  }
  
  if (m_acquisitionContext.acquisitionState != ACQUISITION_STATE_SYSTEM_ACTIVE)
  {
    return 0;
  }
  
  currentTime = HAL_GetTick();
  
  /* Check if it's time for next sample */
  if (currentTime >= m_acquisitionContext.nextSampleTimestamp)
  {
    return 1;
  }
  
  return 0;
}

/**
 * @brief Execute sampling task
 */
void ExecuteSamplingTask(void)
{
  float voltageReading;
  uint32_t currentTime;
  uint8_t isOverlimit;
  
  if (!m_acquisitionInitialized)
  {
    return;
  }
  
  /* Check if sampling should be performed */
  if (!ShouldPerformSampling())
  {
    return;
  }
  
  /* Get current voltage reading from analog sampler */
  voltageReading = GetCurrentVoltageReading();
  currentTime = HAL_GetTick();
  
  /* Update acquisition context */
  m_acquisitionContext.currentVoltageReading = voltageReading;
  m_acquisitionContext.lastSampleTimestamp = currentTime;
  m_acquisitionContext.nextSampleTimestamp = currentTime + 
                                             m_samplingIntervalMs[m_acquisitionContext.samplingInterval];
  
  /* Check for overlimit condition */
  isOverlimit = CheckVoltageOverlimit(voltageReading);
  
  /* Handle data storage */
  HandleDataStorageOperations(voltageReading, currentTime, isOverlimit);
  
  /* Update sample counter */
  m_acquisitionContext.totalSampleCount++;
  
  /* Update indicator blink */
  UpdateIndicatorBlink();
  
  DEBUG_PRINT("Sample collected: %.3fV at %lu ms", voltageReading, currentTime);
}

/* Additional function implementations would continue here... */
/* This is a framework - full implementation will be added during actual refactoring */