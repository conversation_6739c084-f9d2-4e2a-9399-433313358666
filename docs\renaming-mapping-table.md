# 防查重重命名映射表

## 文件重命名映射

### 应用层文件重命名
| 原文件名 | 新文件名 | 新路径 | 功能描述 |
|---------|---------|--------|----------|
| scheduler.c | TaskManager.c | SystemCore/Managers/ | 任务调度管理器 |
| scheduler.h | TaskManager.h | SystemCore/Managers/ | 任务调度管理器头文件 |
| adc_app.c | AnalogSampler.c | SystemCore/Controllers/ | 模拟信号采样器 |
| adc_app.h | AnalogSampler.h | SystemCore/Controllers/ | 模拟信号采样器头文件 |
| led_app.c | StatusIndicator.c | SystemCore/Controllers/ | 状态指示器 |
| led_app.h | StatusIndicator.h | SystemCore/Controllers/ | 状态指示器头文件 |
| btn_app.c | InputHandler.c | SystemCore/Utilities/ | 输入处理器 |
| btn_app.h | InputHandler.h | SystemCore/Utilities/ | 输入处理器头文件 |
| usart_app.c | CommunicationInterface.c | SystemCore/Interfaces/ | 通信接口 |
| usart_app.h | CommunicationInterface.h | SystemCore/Interfaces/ | 通信接口头文件 |
| sampling_control.c | DataAcquisitionManager.c | SystemCore/Controllers/ | 数据采集管理器 |
| sampling_control.h | DataAcquisitionManager.h | SystemCore/Controllers/ | 数据采集管理器头文件 |
| config_manager.c | SystemConfiguration.c | SystemCore/Managers/ | 系统配置管理器 |
| config_manager.h | SystemConfiguration.h | SystemCore/Managers/ | 系统配置管理器头文件 |
| data_storage.c | PersistentStorage.c | SystemCore/Managers/ | 持久化存储管理器 |
| data_storage.h | PersistentStorage.h | SystemCore/Managers/ | 持久化存储管理器头文件 |
| oled_app.c | DisplayManager.c | SystemCore/Utilities/ | 显示管理器 |
| oled_app.h | DisplayManager.h | SystemCore/Utilities/ | 显示管理器头文件 |
| rtc_app.c | TimeKeeper.c | SystemCore/Utilities/ | 时间管理器 |
| rtc_app.h | TimeKeeper.h | SystemCore/Utilities/ | 时间管理器头文件 |
| flash_app.c | StorageInterface.c | SystemCore/Utilities/ | 存储接口 |
| flash_app.h | StorageInterface.h | SystemCore/Utilities/ | 存储接口头文件 |
| system_check.c | SystemDiagnostics.c | SystemCore/Utilities/ | 系统诊断 |
| system_check.h | SystemDiagnostics.h | SystemCore/Utilities/ | 系统诊断头文件 |
| device_id.c | DeviceIdentifier.c | SystemCore/Utilities/ | 设备标识符 |
| device_id.h | DeviceIdentifier.h | SystemCore/Utilities/ | 设备标识符头文件 |
| ini_parser.c | ConfigurationParser.c | SystemCore/Utilities/ | 配置解析器 |
| ini_parser.h | ConfigurationParser.h | SystemCore/Utilities/ | 配置解析器头文件 |
| mydefine.h | SystemDefinitions.h | SystemCore/Interfaces/ | 系统定义 |

## 函数重命名映射

### 任务调度系统 (TaskManager)
| 原函数名 | 新函数名 | 功能描述 |
|---------|---------|----------|
| scheduler_init | InitializeSystemScheduler | 初始化系统调度器 |
| scheduler_run | ExecuteTaskScheduler | 执行任务调度器 |

### ADC采样模块 (AnalogSampler)
| 原函数名 | 新函数名 | 功能描述 |
|---------|---------|----------|
| adc_task | ProcessAnalogSampling | 处理模拟采样 |
| adc_tim_dma_init | InitializeDmaTimerSampling | 初始化DMA定时器采样 |
| adc_dma_init | InitializeDmaAnalogSampling | 初始化DMA模拟采样 |
| HAL_ADC_ConvCpltCallback | OnAnalogConversionComplete | 模拟转换完成回调 |

### LED控制模块 (StatusIndicator)
| 原函数名 | 新函数名 | 功能描述 |
|---------|---------|----------|
| led_task | UpdateStatusIndicators | 更新状态指示器 |
| led_disp | DisplayLedPattern | 显示LED模式 |

### 按键处理模块 (InputHandler)
| 原函数名 | 新函数名 | 功能描述 |
|---------|---------|----------|
| btn_task | ProcessInputEvents | 处理输入事件 |
| app_btn_init | InitializeInputHandler | 初始化输入处理器 |
| prv_btn_get_state | ReadButtonState | 读取按键状态 |
| prv_btn_event | HandleButtonEvent | 处理按键事件 |

### 串口通信模块 (CommunicationInterface)
| 原函数名 | 新函数名 | 功能描述 |
|---------|---------|----------|
| uart_task | ProcessCommunication | 处理通信 |
| parse_uart_command | ParseIncomingCommand | 解析传入命令 |
| my_printf | TransmitFormattedData | 传输格式化数据 |
| handle_conf_command | ProcessConfigurationCommand | 处理配置命令 |
| handle_ratio_command | ProcessRatioCommand | 处理比率命令 |
| handle_limit_command | ProcessLimitCommand | 处理限制命令 |
| handle_start_command | ProcessStartCommand | 处理启动命令 |
| handle_stop_command | ProcessStopCommand | 处理停止命令 |
| handle_hide_command | ProcessHideCommand | 处理隐藏命令 |
| handle_unhide_command | ProcessUnhideCommand | 处理取消隐藏命令 |
| format_hex_output | FormatHexadecimalOutput | 格式化十六进制输出 |
| convert_rtc_to_unix_timestamp | ConvertRtcToUnixTimestamp | 转换RTC到Unix时间戳 |

### 采样控制系统 (DataAcquisitionManager)
| 原函数名 | 新函数名 | 功能描述 |
|---------|---------|----------|
| sampling_init | InitializeDataAcquisition | 初始化数据采集 |
| sampling_start | BeginDataCollection | 开始数据收集 |
| sampling_stop | TerminateDataCollection | 终止数据收集 |
| sampling_set_cycle | ConfigureSamplingInterval | 配置采样间隔 |
| sampling_get_state | RetrieveAcquisitionState | 获取采集状态 |
| sampling_get_cycle | RetrieveSamplingInterval | 获取采样间隔 |
| sampling_should_sample | ShouldPerformSampling | 是否应该执行采样 |
| sampling_update_led_blink | UpdateIndicatorBlink | 更新指示器闪烁 |
| sampling_get_voltage | RetrieveVoltageReading | 获取电压读数 |
| sampling_check_overlimit | CheckVoltageOverlimit | 检查电压超限 |
| sampling_task | ExecuteSamplingTask | 执行采样任务 |
| sampling_get_led_blink_state | RetrieveIndicatorBlinkState | 获取指示器闪烁状态 |
| sampling_handle_data_storage | HandleDataStorageOperations | 处理数据存储操作 |

### 配置管理系统 (SystemConfiguration)
| 原函数名 | 新函数名 | 功能描述 |
|---------|---------|----------|
| config_init | InitializeSystemConfiguration | 初始化系统配置 |
| config_get_params | RetrieveConfigurationParameters | 获取配置参数 |
| config_set_params | UpdateConfigurationParameters | 更新配置参数 |
| config_save_to_flash | SaveConfigurationToFlash | 保存配置到Flash |
| config_load_from_flash | LoadConfigurationFromFlash | 从Flash加载配置 |
| config_reset_to_default | ResetConfigurationToDefault | 重置配置为默认值 |
| config_validate_ratio | ValidateRatioParameter | 验证比率参数 |
| config_validate_limit | ValidateLimitParameter | 验证限制参数 |
| config_validate_sampling_cycle | ValidateSamplingCycleParameter | 验证采样周期参数 |
| config_set_sampling_cycle | ConfigureSamplingCycle | 配置采样周期 |
| config_get_sampling_cycle | RetrieveSamplingCycle | 获取采样周期 |
| config_calculate_crc32 | CalculateConfigurationCrc32 | 计算配置CRC32 |

### 数据存储系统 (PersistentStorage)
| 原函数名 | 新函数名 | 功能描述 |
|---------|---------|----------|
| data_storage_init | InitializePersistentStorage | 初始化持久化存储 |
| data_storage_write_sample | StoreSampleData | 存储样本数据 |
| data_storage_write_overlimit | StoreOverlimitData | 存储超限数据 |
| data_storage_write_log | RecordSystemLog | 记录系统日志 |
| data_storage_write_hidedata | StoreHiddenData | 存储隐藏数据 |
| generate_datetime_string | GenerateDateTimeString | 生成日期时间字符串 |
| generate_filename | GenerateStorageFilename | 生成存储文件名 |
| data_storage_test | TestPersistentStorage | 测试持久化存储 |

## 变量重命名映射

### 全局变量
| 原变量名 | 新变量名 | 类型 | 功能描述 |
|---------|---------|------|----------|
| task_num | m_totalTaskCount | uint8_t | 总任务数量 |
| g_sampling_control | m_systemSamplingManager | sampling_control_t | 系统采样管理器 |
| g_boot_count | m_systemBootCounter | uint32_t | 系统启动计数器 |
| g_cmd_state | m_commandProcessingState | cmd_state_t | 命令处理状态 |
| g_sampling_output_enabled | m_samplingOutputEnabled | uint8_t | 采样输出使能 |
| g_last_output_time | m_lastOutputTimestamp | uint32_t | 最后输出时间戳 |
| g_output_format | m_dataOutputFormat | output_format_t | 数据输出格式 |

### 静态变量
| 原变量名 | 新变量名 | 类型 | 功能描述 |
|---------|---------|------|----------|
| scheduler_task | g_systemTaskArray | task_t[] | 系统任务数组 |
| uart_rx_index | m_receiveBufferIndex | uint16_t | 接收缓冲区索引 |
| uart_rx_ticks | m_receiveTimestamp | uint32_t | 接收时间戳 |
| uart_rx_buffer | m_receiveDataBuffer | uint8_t[] | 接收数据缓冲区 |
| uart_rx_dma_buffer | m_receiveDmaBuffer | uint8_t[] | 接收DMA缓冲区 |
| uart_dma_buffer | m_transmitDmaBuffer | uint8_t[] | 传输DMA缓冲区 |
| uart_flag | m_communicationFlag | uint8_t | 通信标志 |
| uart_ringbuffer | m_communicationRingBuffer | rt_ringbuffer | 通信环形缓冲区 |
| ringbuffer_pool | m_ringBufferMemoryPool | uint8_t[] | 环形缓冲区内存池 |

### ADC相关变量
| 原变量名 | 新变量名 | 类型 | 功能描述 |
|---------|---------|------|----------|
| adc_val | m_analogValue | uint32_t | 模拟值 |
| voltage | m_currentVoltageValue | float | 当前电压值 |
| adc_val_buffer | m_analogDataBuffer | uint32_t[] | 模拟数据缓冲区 |
| dac_val_buffer | m_dacValueBuffer | uint32_t[] | DAC值缓冲区 |
| res_val_buffer | m_resultValueBuffer | uint32_t[] | 结果值缓冲区 |
| AdcConvEnd | m_analogConversionComplete | uint8_t | 模拟转换完成标志 |
| wave_analysis_flag | m_waveformAnalysisFlag | uint8_t | 波形分析标志 |
| wave_query_type | m_waveformQueryType | uint8_t | 波形查询类型 |

### LED相关变量
| 原变量名 | 新变量名 | 类型 | 功能描述 |
|---------|---------|------|----------|
| ucLed | m_indicatorStates | uint8_t[] | 指示器状态 |
| led1_blink_time | m_primaryBlinkTimer | uint32_t | 主要闪烁定时器 |
| led1_blink_state | m_primaryBlinkState | uint8_t | 主要闪烁状态 |

## 结构体和枚举重命名映射

### 结构体重命名
| 原结构体名 | 新结构体名 | 功能描述 |
|-----------|-----------|----------|
| task_t | SystemTaskDescriptor | 系统任务描述符 |
| config_params_t | SystemConfigurationParameters | 系统配置参数 |
| sampling_control_t | DataAcquisitionContext | 数据采集上下文 |
| file_state_t | StorageFileState | 存储文件状态 |

### 枚举重命名
| 原枚举名 | 新枚举名 | 功能描述 |
|---------|---------|----------|
| config_status_t | SystemConfigurationStatus | 系统配置状态 |
| sampling_state_t | DataAcquisitionState | 数据采集状态 |
| sampling_cycle_t | SamplingIntervalType | 采样间隔类型 |
| sampling_status_t | DataAcquisitionStatus | 数据采集状态 |
| data_storage_status_t | PersistentStorageStatus | 持久化存储状态 |
| storage_type_t | DataStorageCategory | 数据存储类别 |
| cmd_state_t | CommandProcessingState | 命令处理状态 |
| output_format_t | DataOutputFormat | 数据输出格式 |
| user_button_t | InputDeviceIdentifier | 输入设备标识符 |

## 宏定义重命名映射

### 配置相关宏
| 原宏名 | 新宏名 | 值 | 功能描述 |
|-------|-------|----|---------| 
| CONFIG_OK | CONFIGURATION_SUCCESS | 0 | 配置成功 |
| CONFIG_ERROR | CONFIGURATION_FAILURE | 1 | 配置失败 |
| CONFIG_INVALID | CONFIGURATION_INVALID | 2 | 配置无效 |
| CONFIG_FLASH_ERROR | CONFIGURATION_FLASH_ERROR | 3 | 配置Flash错误 |
| CONFIG_CRC_ERROR | CONFIGURATION_CRC_ERROR | 4 | 配置CRC错误 |
| CONFIG_FLASH_ADDR | SYSTEM_CONFIG_STORAGE_ADDRESS | 0x1F0000 | 系统配置存储地址 |
| CONFIG_MAGIC | CONFIGURATION_MAGIC_NUMBER | 0x43464721 | 配置魔数 |
| CONFIG_VERSION | CONFIGURATION_VERSION_NUMBER | 0x02 | 配置版本号 |

### 采样相关宏
| 原宏名 | 新宏名 | 值 | 功能描述 |
|-------|-------|----|---------| 
| SAMPLING_OK | DATA_ACQUISITION_SUCCESS | 0 | 数据采集成功 |
| SAMPLING_ERROR | DATA_ACQUISITION_FAILURE | 1 | 数据采集失败 |
| SAMPLING_INVALID | DATA_ACQUISITION_INVALID | 2 | 数据采集无效 |
| SAMPLING_IDLE | ACQUISITION_STATE_IDLE | 0 | 采集状态空闲 |
| SAMPLING_ACTIVE | ACQUISITION_STATE_ACTIVE | 1 | 采集状态活跃 |
| CYCLE_5S | SAMPLING_INTERVAL_FIVE_SECONDS | 5 | 采样间隔5秒 |
| CYCLE_10S | SAMPLING_INTERVAL_TEN_SECONDS | 10 | 采样间隔10秒 |
| CYCLE_15S | SAMPLING_INTERVAL_FIFTEEN_SECONDS | 15 | 采样间隔15秒 |

### ADC相关宏
| 原宏名 | 新宏名 | 值 | 功能描述 |
|-------|-------|----|---------| 
| ADC_MODE | ANALOG_SAMPLING_MODE | 3 | 模拟采样模式 |
| BUFFER_SIZE | SAMPLE_BUFFER_CAPACITY | 2048 | 样本缓冲区容量 |
| ADC_DMA_BUFFER_SIZE | ANALOG_DMA_BUFFER_SIZE | 32 | 模拟DMA缓冲区大小 |

### 存储相关宏
| 原宏名 | 新宏名 | 值 | 功能描述 |
|-------|-------|----|---------| 
| DATA_STORAGE_OK | PERSISTENT_STORAGE_SUCCESS | 0 | 持久化存储成功 |
| DATA_STORAGE_ERROR | PERSISTENT_STORAGE_FAILURE | 1 | 持久化存储失败 |
| DATA_STORAGE_INVALID | PERSISTENT_STORAGE_INVALID | 2 | 持久化存储无效 |
| DATA_STORAGE_NO_SD | PERSISTENT_STORAGE_NO_MEDIA | 3 | 持久化存储无媒体 |
| STORAGE_SAMPLE | DATA_CATEGORY_SAMPLE | 0 | 数据类别样本 |
| STORAGE_OVERLIMIT | DATA_CATEGORY_OVERLIMIT | 1 | 数据类别超限 |
| STORAGE_LOG | DATA_CATEGORY_LOG | 2 | 数据类别日志 |
| STORAGE_HIDEDATA | DATA_CATEGORY_HIDEDATA | 3 | 数据类别隐藏数据 |
| STORAGE_TYPE_COUNT | DATA_CATEGORY_COUNT | 4 | 数据类别数量 |

### 按键相关宏
| 原宏名 | 新宏名 | 值 | 功能描述 |
|-------|-------|----|---------| 
| USER_BUTTON_0 | INPUT_DEVICE_BUTTON_0 | 0 | 输入设备按键0 |
| USER_BUTTON_1 | INPUT_DEVICE_BUTTON_1 | 1 | 输入设备按键1 |
| USER_BUTTON_2 | INPUT_DEVICE_BUTTON_2 | 2 | 输入设备按键2 |
| USER_BUTTON_3 | INPUT_DEVICE_BUTTON_3 | 3 | 输入设备按键3 |
| USER_BUTTON_4 | INPUT_DEVICE_BUTTON_4 | 4 | 输入设备按键4 |
| USER_BUTTON_5 | INPUT_DEVICE_BUTTON_5 | 5 | 输入设备按键5 |
| USER_BUTTON_MAX | INPUT_DEVICE_BUTTON_MAX | 6 | 输入设备按键最大值 |

### 通信相关宏
| 原宏名 | 新宏名 | 值 | 功能描述 |
|-------|-------|----|---------| 
| CMD_STATE_IDLE | COMMAND_PROCESSING_IDLE | 0 | 命令处理空闲 |
| CMD_STATE_WAIT_RATIO | COMMAND_PROCESSING_WAIT_RATIO | 1 | 命令处理等待比率 |
| CMD_STATE_WAIT_LIMIT | COMMAND_PROCESSING_WAIT_LIMIT | 2 | 命令处理等待限制 |
| OUTPUT_FORMAT_NORMAL | DATA_OUTPUT_FORMAT_NORMAL | 0 | 数据输出格式正常 |
| OUTPUT_FORMAT_HIDDEN | DATA_OUTPUT_FORMAT_HIDDEN | 1 | 数据输出格式隐藏 |

## 常量字符串重命名映射

### 目录名称
| 原常量 | 新常量 | 值 | 功能描述 |
|-------|-------|----|---------| 
| "sample" | "SampleData" | "SampleData" | 样本数据目录 |
| "overLimit" | "OverlimitRecords" | "OverlimitRecords" | 超限记录目录 |
| "log" | "SystemLogs" | "SystemLogs" | 系统日志目录 |
| "hideData" | "HiddenData" | "HiddenData" | 隐藏数据目录 |

### 文件名前缀
| 原常量 | 新常量 | 值 | 功能描述 |
|-------|-------|----|---------| 
| "sampleData" | "DataSample" | "DataSample" | 数据样本前缀 |
| "overLimit" | "LimitExceeded" | "LimitExceeded" | 超限前缀 |
| "log" | "SystemLog" | "SystemLog" | 系统日志前缀 |
| "hideData" | "ConcealedData" | "ConcealedData" | 隐藏数据前缀 |

这个重命名映射表确保了所有标识符都有明确的对应关系，为防查重重构提供了完整的指导。