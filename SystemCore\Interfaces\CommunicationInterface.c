/**
 * @file CommunicationInterface.c
 * @brief Serial communication and command processing implementation
 * <AUTHOR> Systems Team
 * @date 2025-01-15
 * @copyright Copyright (c) 2025 Advanced Embedded Solutions. All rights reserved.
 */

#include "CommunicationInterface.h"
#include "DataAcquisitionManager.h"
#include "SystemConfiguration.h"
#include <stdarg.h>

/* External Hardware Handles */
extern UART_HandleTypeDef huart1;
extern DMA_HandleTypeDef hdma_usart1_rx;
extern DMA_HandleTypeDef hdma_usart1_tx;

/* Private Variables */
static CommunicationContext m_communicationContext;
static uint8_t m_communicationInitialized = 0;

/* Command Table */
static const CommandDefinition m_commandTable[] = 
{
  {"CONF", ProcessConfigurationCommand, "Configuration command", 1},
  {"RATIO", ProcessRatioCommand, "Set voltage ratio", 1},
  {"LIMIT", ProcessLimitCommand, "Set voltage limit", 1},
  {"START", ProcessStartCommand, "Start sampling", 0},
  {"STOP", ProcessStopCommand, "Stop sampling", 0},
  {"HIDE", ProcessHideCommand, "Hide data output", 0},
  {"UNHIDE", ProcessUnhideCommand, "Show data output", 0},
  {NULL, NULL, NULL, 0} /* End of table marker */
};

/**
 * @brief Initialize communication interface
 */
DataAcquisitionStatus InitializeCommunicationInterface(void)
{
  /* Clear communication context */
  ZERO_MEMORY(&m_communicationContext, sizeof(CommunicationContext));
  
  /* Initialize ring buffer */
  rt_ringbuffer_init(&m_communicationContext.m_communicationRingBuffer,
                     m_communicationContext.m_ringBufferMemoryPool,
                     COMMUNICATION_BUFFER_SIZE);
  
  /* Set default communication state */
  m_communicationContext.m_commandProcessingState = COMMAND_STATE_IDLE;
  m_communicationContext.m_samplingOutputEnabled = 1;
  m_communicationContext.m_dataOutputFormat = OUTPUT_FORMAT_NORMAL;
  m_communicationContext.m_communicationFlag = 0;
  m_communicationContext.m_receiveBufferIndex = 0;
  m_communicationContext.m_receiveTimestamp = HAL_GetTick();
  m_communicationContext.m_lastOutputTimestamp = HAL_GetTick();
  
  /* Start UART DMA reception */
  if (HAL_UART_Receive_DMA(&huart1, 
                           m_communicationContext.m_receiveDmaBuffer, 
                           COMMUNICATION_DMA_BUFFER_SIZE) != HAL_OK)
  {
    ERROR_PRINT("Failed to start UART DMA reception");
    return DATA_ACQUISITION_FAILURE;
  }
  
  m_communicationInitialized = 1;
  
  INFO_PRINT("Communication interface initialized");
  
  return DATA_ACQUISITION_SUCCESS;
}

/**
 * @brief Process communication operations
 */
void ProcessCommunication(void)
{
  uint32_t currentTime;
  uint32_t bytesAvailable;
  char commandBuffer[COMMUNICATION_COMMAND_MAX_LENGTH];
  uint32_t bytesRead;
  
  if (!m_communicationInitialized)
  {
    ERROR_PRINT("Communication interface not initialized");
    return;
  }
  
  currentTime = HAL_GetTick();
  
  /* Check for received data in ring buffer */
  bytesAvailable = rt_ringbuffer_data_len(&m_communicationContext.m_communicationRingBuffer);
  
  if (bytesAvailable > 0)
  {
    /* Read command from ring buffer */
    bytesRead = rt_ringbuffer_get(&m_communicationContext.m_communicationRingBuffer,
                                  (uint8_t*)commandBuffer,
                                  MIN(bytesAvailable, sizeof(commandBuffer) - 1));
    
    if (bytesRead > 0)
    {
      /* Null-terminate command string */
      commandBuffer[bytesRead] = '\0';
      
      /* Process the command */
      ParseIncomingCommand(commandBuffer);
      
      DEBUG_PRINT("Processed command: %s", commandBuffer);
    }
  }
  
  /* Handle periodic sampling output */
  if (m_communicationContext.m_samplingOutputEnabled)
  {
    DataAcquisitionState acquisitionState = RetrieveAcquisitionState();
    
    if (acquisitionState == ACQUISITION_STATE_SYSTEM_ACTIVE)
    {
      /* Output sampling data periodically */
      if ((currentTime - m_communicationContext.m_lastOutputTimestamp) >= 1000) /* 1 second interval */
      {
        float currentVoltage = RetrieveVoltageReading();
        
        if (m_communicationContext.m_dataOutputFormat == OUTPUT_FORMAT_NORMAL)
        {
          TransmitFormattedData("SAMPLE: %.3fV at %lu ms\r\n", currentVoltage, currentTime);
        }
        
        m_communicationContext.m_lastOutputTimestamp = currentTime;
      }
    }
  }
}

/**
 * @brief Parse incoming command
 */
uint8_t ParseIncomingCommand(const char* commandString)
{
  const CommandDefinition* currentCommand;
  char commandName[32];
  char commandArgs[COMMUNICATION_COMMAND_MAX_LENGTH];
  char responseBuffer[COMMUNICATION_RESPONSE_MAX_LENGTH];
  uint8_t commandFound = 0;
  uint8_t processingResult;
  
  if (commandString == NULL)
  {
    ERROR_PRINT("Invalid command string");
    return COMMAND_PROCESSING_ERROR;
  }
  
  /* Parse command name and arguments */
  if (sscanf(commandString, "%31s %63s", commandName, commandArgs) < 1)
  {
    ERROR_PRINT("Failed to parse command");
    return COMMAND_PROCESSING_ERROR;
  }
  
  /* Search for command in command table */
  currentCommand = m_commandTable;
  while (currentCommand->commandName != NULL)
  {
    if (strcmp(commandName, currentCommand->commandName) == 0)
    {
      commandFound = 1;
      
      /* Execute command handler */
      processingResult = currentCommand->handlerFunction(commandArgs, 
                                                         responseBuffer, 
                                                         sizeof(responseBuffer));
      
      /* Send response */
      if (processingResult == COMMAND_PROCESSING_COMPLETE)
      {
        SendResponseMessage(responseBuffer);
      }
      else
      {
        SendResponseMessage("ERROR: Command processing failed\r\n");
      }
      
      break;
    }
    currentCommand++;
  }
  
  if (!commandFound)
  {
    ERROR_PRINT("Unknown command: %s", commandName);
    SendResponseMessage("ERROR: Unknown command\r\n");
    return COMMAND_PROCESSING_ERROR;
  }
  
  return COMMAND_PROCESSING_COMPLETE;
}

/**
 * @brief Transmit formatted data
 */
uint32_t TransmitFormattedData(const char* format, ...)
{
  va_list args;
  char transmitBuffer[COMMUNICATION_RESPONSE_MAX_LENGTH];
  uint32_t bytesFormatted;
  
  if (!m_communicationInitialized)
  {
    return 0;
  }
  
  /* Format the string */
  va_start(args, format);
  bytesFormatted = vsnprintf(transmitBuffer, sizeof(transmitBuffer), format, args);
  va_end(args);
  
  /* Transmit via UART */
  if (HAL_UART_Transmit(&huart1, (uint8_t*)transmitBuffer, bytesFormatted, COMMUNICATION_TIMEOUT_MS) == HAL_OK)
  {
    return bytesFormatted;
  }
  
  return 0;
}

/**
 * @brief Process start command
 */
uint8_t ProcessStartCommand(const char* commandArgs, char* responseBuffer, uint32_t responseBufferSize)
{
  DataAcquisitionStatus result;
  
  /* Start data acquisition */
  result = BeginDataCollection();
  
  if (result == DATA_ACQUISITION_SUCCESS)
  {
    snprintf(responseBuffer, responseBufferSize, "OK: Sampling started\r\n");
    return COMMAND_PROCESSING_COMPLETE;
  }
  else
  {
    snprintf(responseBuffer, responseBufferSize, "ERROR: Failed to start sampling\r\n");
    return COMMAND_PROCESSING_ERROR;
  }
}

/**
 * @brief Process stop command
 */
uint8_t ProcessStopCommand(const char* commandArgs, char* responseBuffer, uint32_t responseBufferSize)
{
  DataAcquisitionStatus result;
  
  /* Stop data acquisition */
  result = TerminateDataCollection();
  
  if (result == DATA_ACQUISITION_SUCCESS)
  {
    snprintf(responseBuffer, responseBufferSize, "OK: Sampling stopped\r\n");
    return COMMAND_PROCESSING_COMPLETE;
  }
  else
  {
    snprintf(responseBuffer, responseBufferSize, "ERROR: Failed to stop sampling\r\n");
    return COMMAND_PROCESSING_ERROR;
  }
}

/* Additional function implementations would continue here... */
/* This is a framework - full implementation will be added during actual refactoring */