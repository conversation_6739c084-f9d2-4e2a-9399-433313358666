# GD32工程防查重策略文档

## 项目概述
基于现有GD32工程，通过系统性的代码重构、重命名、重组织生成防查重工程副本。保持功能逻辑完全不变，通过7个维度的全面重构将代码相似度降低到5%以下。

## 现有工程代码特征分析

### 1. 文件组织结构
- **主目录**: APP (18个模块文件)
- **文件命名**: snake_case格式 (adc_app.c, led_app.h, config_manager.c)
- **模块分类**: 按功能分散在单一目录中

### 2. 函数命名规范
- **格式**: snake_case (下划线分隔)
- **模式**: [模块名]_[动作] 
- **示例**:
  - scheduler_init() → 调度器初始化
  - adc_task() → ADC任务处理
  - data_storage_write_sample() → 数据存储写入样本
  - config_get_params() → 配置获取参数
  - sampling_control_start() → 采样控制启动

### 3. 变量命名规范
- **全局变量**: g_前缀 (g_sampling_control, g_boot_count)
- **静态变量**: 无特定前缀 (task_num, uart_rx_buffer)
- **结构体成员**: 直接命名 (state, cycle, last_sample_time)
- **局部变量**: 简短描述性名称 (result, current_time, voltage)

### 4. 宏定义和常量
- **格式**: 全大写+下划线
- **示例**:
  - CONFIG_FLASH_ADDR, CONFIG_MAGIC
  - ADC_MODE, BUFFER_SIZE
  - STORAGE_TYPE_COUNT, DATA_STORAGE_OK
  - SAMPLING_ACTIVE, CYCLE_5S

### 5. 结构体和枚举命名
- **结构体**: typedef struct + _t后缀
  - config_params_t, sampling_control_t, file_state_t
- **枚举**: typedef enum + _t后缀
  - config_status_t, sampling_state_t, storage_type_t

### 6. 注释风格特征
- **格式**: 中文单行注释 "//"
- **位置**: 行尾注释为主
- **内容**: "功能：...，参数：...，返回：..."
- **文件头**: 中文版权信息和功能描述

### 7. 代码格式特征
- **缩进**: 4个空格
- **大括号**: Linux风格 (函数名同行)
- **空格**: 操作符两边有空格
- **换行**: 函数参数较多时不换行

## 防查重重构策略

### 1. 目录结构重组
```
原结构: APP/
新结构: SystemCore/
├── Managers/          # 管理类模块
│   ├── TaskManager.*     # scheduler.*
│   ├── SystemConfiguration.*  # config_manager.*
│   └── PersistentStorage.*    # data_storage.*
├── Controllers/       # 控制类模块
│   ├── AnalogSampler.*       # adc_app.*
│   ├── DataAcquisitionManager.*  # sampling_control.*
│   └── StatusIndicator.*     # led_app.*
├── Utilities/         # 工具类模块
│   ├── InputHandler.*        # btn_app.*
│   ├── DisplayManager.*      # oled_app.*
│   ├── TimeKeeper.*          # rtc_app.*
│   └── StorageInterface.*    # flash_app.*
└── Interfaces/        # 接口类模块
    ├── CommunicationInterface.*  # usart_app.*
    └── SystemDefinitions.h      # mydefine.h
```

### 2. 函数重命名映射表

#### 核心调度系统
- scheduler_init() → InitializeSystemScheduler()
- scheduler_run() → ExecuteTaskScheduler()
- task_t → SystemTaskDescriptor

#### ADC采样模块
- adc_task() → ProcessAnalogSampling()
- adc_tim_dma_init() → InitializeDmaTimerSampling()
- adc_val_buffer → m_analogDataBuffer
- voltage → m_currentVoltageValue

#### LED控制模块
- led_task() → UpdateStatusIndicators()
- led_disp() → DisplayLedPattern()
- ucLed → m_indicatorStates

#### 按键处理模块
- btn_task() → ProcessInputEvents()
- prv_btn_get_state() → ReadButtonState()
- prv_btn_event() → HandleButtonEvent()
- user_button_t → InputDeviceIdentifier

#### 串口通信模块
- uart_task() → ProcessCommunication()
- parse_uart_command() → ParseIncomingCommand()
- my_printf() → TransmitFormattedData()
- uart_rx_buffer → m_receiveDataBuffer

#### 采样控制系统
- sampling_init() → InitializeDataAcquisition()
- sampling_start() → BeginDataCollection()
- sampling_stop() → TerminateDataCollection()
- sampling_control_t → DataAcquisitionContext

#### 配置管理系统
- config_init() → InitializeSystemConfiguration()
- config_get_params() → RetrieveConfigurationParameters()
- config_set_params() → UpdateConfigurationParameters()
- config_params_t → SystemConfigurationParameters

#### 数据存储系统
- data_storage_init() → InitializePersistentStorage()
- data_storage_write_sample() → StoreSampleData()
- data_storage_write_log() → RecordSystemLog()
- storage_type_t → DataStorageCategory

### 3. 变量重命名规范

#### 全局变量命名转换
- g_sampling_control → m_systemSamplingManager
- g_boot_count → m_systemBootCounter
- g_cmd_state → m_commandProcessingState
- g_output_format → m_dataOutputFormat

#### 静态变量命名转换
- task_num → m_totalTaskCount
- scheduler_task → g_systemTaskArray
- uart_rx_index → m_receiveBufferIndex
- led1_blink_time → m_primaryBlinkTimer

### 4. 宏定义重命名映射

#### 配置相关宏
- CONFIG_OK → CONFIGURATION_SUCCESS
- CONFIG_ERROR → CONFIGURATION_FAILURE
- CONFIG_FLASH_ADDR → SYSTEM_CONFIG_STORAGE_ADDRESS
- CONFIG_MAGIC → CONFIGURATION_MAGIC_NUMBER

#### 采样相关宏
- SAMPLING_OK → DATA_ACQUISITION_SUCCESS
- SAMPLING_ACTIVE → ACQUISITION_STATE_ACTIVE
- CYCLE_5S → SAMPLING_INTERVAL_FIVE_SECONDS
- ADC_MODE → ANALOG_SAMPLING_MODE

#### 存储相关宏
- DATA_STORAGE_OK → PERSISTENT_STORAGE_SUCCESS
- STORAGE_SAMPLE → DATA_CATEGORY_SAMPLE
- BUFFER_SIZE → SAMPLE_BUFFER_CAPACITY

### 5. 代码风格转换规则

#### 缩进和格式
- 原格式: 4空格缩进
- 新格式: 2空格 + Tab混合缩进

#### 大括号风格
- 原格式: Linux风格 (同行)
```c
void function() {
    // code
}
```
- 新格式: Allman风格 (独立行)
```c
void Function()
{
  // code
}
```

#### 注释风格转换
- 原格式: 中文单行注释
```c
void adc_task(void) // ADC轮询采样任务 参数:无 返回:无
```
- 新格式: 英文多行Doxygen注释
```c
/**
 * @brief Process analog sampling operations
 * @param None
 * @return None
 * @note This function handles ADC polling-based sampling
 */
void ProcessAnalogSampling(void)
```

### 6. 算法实现变化策略

#### 条件判断重构
- 原实现: if-else链
- 新实现: switch-case或函数指针表

#### 循环结构调整
- 原实现: for循环
- 新实现: while循环，或反向循环

#### 数据结构优化
- 原实现: 静态数组
- 新实现: 结构化数据管理

#### 错误处理方式
- 原实现: 返回错误码
- 新实现: 回调函数通知

### 7. 文档和注释重写规范

#### 文件头注释模板
```c
/**
 * @file SystemConfiguration.c
 * @brief System configuration management implementation
 * <AUTHOR> Systems Team
 * @date 2025-01-15
 * @copyright Copyright (c) 2025 Advanced Embedded Solutions. All rights reserved.
 * 
 * This module provides comprehensive configuration management capabilities
 * including parameter validation, persistent storage, and runtime updates.
 */
```

#### 函数注释模板
```c
/**
 * @brief Initialize the system configuration manager
 * @details This function sets up the configuration system, loads parameters
 *          from persistent storage, and validates all configuration values
 * @param None
 * @return SystemConfigurationStatus Configuration initialization result
 * @retval CONFIGURATION_SUCCESS Initialization completed successfully
 * @retval CONFIGURATION_FAILURE Initialization failed
 * @note This function must be called before any other configuration operations
 * @see RetrieveConfigurationParameters(), UpdateConfigurationParameters()
 */
SystemConfigurationStatus InitializeSystemConfiguration(void);
```

## 实施优先级和阶段规划

### 第一阶段: 基础重构 (优先级: 高)
1. 创建新目录结构
2. 文件和函数重命名
3. 基本编译验证

### 第二阶段: 风格调整 (优先级: 中)
1. 代码格式转换
2. 注释风格重写
3. 变量命名统一

### 第三阶段: 深度重构 (优先级: 中)
1. 算法实现优化
2. 数据结构调整
3. 错误处理重构

### 第四阶段: 最终验证 (优先级: 高)
1. 功能完整性测试
2. 性能对比验证
3. 相似度检测确认

## 风险控制措施

### 功能一致性保证
- 每个模块重构后立即进行功能测试
- 保持HAL库接口调用方式不变
- 确保任务调度时序不受影响

### 编译兼容性确保
- 分阶段编译验证
- 保持项目配置文件同步更新
- 确保所有依赖关系正确

### 性能影响控制
- 在嵌入式环境限制下谨慎使用动态内存
- 保持实时性要求不变
- 监控内存使用情况

## 预期效果评估

### 相似度降低目标
- **函数名相似度**: < 5%
- **变量名相似度**: < 3%
- **代码结构相似度**: < 8%
- **注释内容相似度**: < 2%
- **整体代码相似度**: < 5%

### 功能一致性保证
- 所有功能模块100%兼容
- 性能指标无差异
- 用户接口完全一致
- 数据格式保持兼容

通过以上7个维度的全面重构，预计可以有效避免各种查重检测算法，同时保持100%的功能一致性。