/**
 * @file SystemDefinitions.h
 * @brief Global system definitions and includes for embedded application
 * <AUTHOR> Systems Team
 * @date 2025-01-15
 * @copyright Copyright (c) 2025 Advanced Embedded Solutions. All rights reserved.
 * 
 * This header file provides comprehensive system-wide definitions, constants,
 * and includes for the entire embedded application framework.
 */

#ifndef SYSTEM_DEFINITIONS_H
#define SYSTEM_DEFINITIONS_H

/* Standard Library Includes */
#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <stdlib.h>
#include <math.h>

/* HAL Driver Includes */
#include "main.h"
#include "stm32f4xx_hal.h"
#include "stm32f4xx_hal_gpio.h"
#include "stm32f4xx_hal_adc.h"
#include "stm32f4xx_hal_dac.h"
#include "stm32f4xx_hal_dma.h"
#include "stm32f4xx_hal_tim.h"
#include "stm32f4xx_hal_uart.h"
#include "stm32f4xx_hal_usart.h"
#include "stm32f4xx_hal_rtc.h"
#include "stm32f4xx_hal_spi.h"
#include "stm32f4xx_hal_i2c.h"

/* Component Library Includes */
#include "oled.h"
#include "ebtn.h"
#include "rt_ringbuffer.h"
#include "WouoUI.h"
#include "lfs.h"
#include "arm_math.h"
#include "fatfs.h"

/* System Configuration Constants */
#define SYSTEM_CONFIG_STORAGE_ADDRESS    0x1F0000
#define CONFIGURATION_MAGIC_NUMBER       0x43464721
#define CONFIGURATION_VERSION_NUMBER     0x02

/* Data Acquisition Constants */
#define ANALOG_SAMPLING_MODE             3
#define SAMPLE_BUFFER_CAPACITY           2048
#define ANALOG_DMA_BUFFER_SIZE           32

/* Sampling Interval Definitions */
#define SAMPLING_INTERVAL_FIVE_SECONDS   5
#define SAMPLING_INTERVAL_TEN_SECONDS    10
#define SAMPLING_INTERVAL_FIFTEEN_SECONDS 15

/* Input Device Constants */
#define INPUT_DEVICE_BUTTON_0            0
#define INPUT_DEVICE_BUTTON_1            1
#define INPUT_DEVICE_BUTTON_2            2
#define INPUT_DEVICE_BUTTON_3            3
#define INPUT_DEVICE_BUTTON_4            4
#define INPUT_DEVICE_BUTTON_5            5
#define INPUT_DEVICE_BUTTON_MAX          6

/* System Status Codes */
#define CONFIGURATION_SUCCESS            0
#define CONFIGURATION_FAILURE            1
#define CONFIGURATION_INVALID            2
#define CONFIGURATION_FLASH_ERROR        3
#define CONFIGURATION_CRC_ERROR          4

#define DATA_ACQUISITION_SUCCESS         0
#define DATA_ACQUISITION_FAILURE         1
#define DATA_ACQUISITION_INVALID         2

#define PERSISTENT_STORAGE_SUCCESS       0
#define PERSISTENT_STORAGE_FAILURE       1
#define PERSISTENT_STORAGE_INVALID       2
#define PERSISTENT_STORAGE_NO_MEDIA      3

/* System State Definitions */
#define ACQUISITION_STATE_IDLE           0
#define ACQUISITION_STATE_ACTIVE         1

#define COMMAND_PROCESSING_IDLE          0
#define COMMAND_PROCESSING_WAIT_RATIO    1
#define COMMAND_PROCESSING_WAIT_LIMIT    2

#define DATA_OUTPUT_FORMAT_NORMAL        0
#define DATA_OUTPUT_FORMAT_HIDDEN        1

/* Data Storage Categories */
#define DATA_CATEGORY_SAMPLE             0
#define DATA_CATEGORY_OVERLIMIT          1
#define DATA_CATEGORY_LOG                2
#define DATA_CATEGORY_HIDEDATA           3
#define DATA_CATEGORY_COUNT              4

/* System Type Definitions */
typedef enum
{
  CONFIGURATION_STATUS_SUCCESS = CONFIGURATION_SUCCESS,
  CONFIGURATION_STATUS_FAILURE = CONFIGURATION_FAILURE,
  CONFIGURATION_STATUS_INVALID = CONFIGURATION_INVALID,
  CONFIGURATION_STATUS_FLASH_ERROR = CONFIGURATION_FLASH_ERROR,
  CONFIGURATION_STATUS_CRC_ERROR = CONFIGURATION_CRC_ERROR
} SystemConfigurationStatus;

typedef enum
{
  ACQUISITION_STATUS_SUCCESS = DATA_ACQUISITION_SUCCESS,
  ACQUISITION_STATUS_FAILURE = DATA_ACQUISITION_FAILURE,
  ACQUISITION_STATUS_INVALID = DATA_ACQUISITION_INVALID
} DataAcquisitionStatus;

typedef enum
{
  ACQUISITION_STATE_SYSTEM_IDLE = ACQUISITION_STATE_IDLE,
  ACQUISITION_STATE_SYSTEM_ACTIVE = ACQUISITION_STATE_ACTIVE
} DataAcquisitionState;

typedef enum
{
  SAMPLING_INTERVAL_TYPE_FIVE = SAMPLING_INTERVAL_FIVE_SECONDS,
  SAMPLING_INTERVAL_TYPE_TEN = SAMPLING_INTERVAL_TEN_SECONDS,
  SAMPLING_INTERVAL_TYPE_FIFTEEN = SAMPLING_INTERVAL_FIFTEEN_SECONDS
} SamplingIntervalType;

typedef enum
{
  STORAGE_STATUS_SUCCESS = PERSISTENT_STORAGE_SUCCESS,
  STORAGE_STATUS_FAILURE = PERSISTENT_STORAGE_FAILURE,
  STORAGE_STATUS_INVALID = PERSISTENT_STORAGE_INVALID,
  STORAGE_STATUS_NO_MEDIA = PERSISTENT_STORAGE_NO_MEDIA
} PersistentStorageStatus;

typedef enum
{
  DATA_STORAGE_CATEGORY_SAMPLE = DATA_CATEGORY_SAMPLE,
  DATA_STORAGE_CATEGORY_OVERLIMIT = DATA_CATEGORY_OVERLIMIT,
  DATA_STORAGE_CATEGORY_LOG = DATA_CATEGORY_LOG,
  DATA_STORAGE_CATEGORY_HIDEDATA = DATA_CATEGORY_HIDEDATA
} DataStorageCategory;

typedef enum
{
  COMMAND_STATE_IDLE = COMMAND_PROCESSING_IDLE,
  COMMAND_STATE_WAIT_RATIO = COMMAND_PROCESSING_WAIT_RATIO,
  COMMAND_STATE_WAIT_LIMIT = COMMAND_PROCESSING_WAIT_LIMIT
} CommandProcessingState;

typedef enum
{
  OUTPUT_FORMAT_NORMAL = DATA_OUTPUT_FORMAT_NORMAL,
  OUTPUT_FORMAT_HIDDEN = DATA_OUTPUT_FORMAT_HIDDEN
} DataOutputFormat;

typedef enum
{
  INPUT_BUTTON_0 = INPUT_DEVICE_BUTTON_0,
  INPUT_BUTTON_1 = INPUT_DEVICE_BUTTON_1,
  INPUT_BUTTON_2 = INPUT_DEVICE_BUTTON_2,
  INPUT_BUTTON_3 = INPUT_DEVICE_BUTTON_3,
  INPUT_BUTTON_4 = INPUT_DEVICE_BUTTON_4,
  INPUT_BUTTON_5 = INPUT_DEVICE_BUTTON_5
} InputDeviceIdentifier;

/* Forward Declarations */
typedef struct SystemTaskDescriptor SystemTaskDescriptor;
typedef struct SystemConfigurationParameters SystemConfigurationParameters;
typedef struct DataAcquisitionContext DataAcquisitionContext;
typedef struct StorageFileState StorageFileState;

/* Global Variable Declarations */
extern uint8_t m_totalTaskCount;
extern SystemTaskDescriptor g_systemTaskArray[];
extern DataAcquisitionContext m_systemSamplingManager;
extern uint32_t m_systemBootCounter;
extern CommandProcessingState m_commandProcessingState;
extern uint8_t m_samplingOutputEnabled;
extern uint32_t m_lastOutputTimestamp;
extern DataOutputFormat m_dataOutputFormat;

/* Function Pointer Type Definitions */
typedef void (*SystemTaskFunction)(void);
typedef void (*SystemEventCallback)(void);
typedef SystemConfigurationStatus (*ConfigurationHandler)(void);
typedef DataAcquisitionStatus (*SamplingHandler)(void);
typedef PersistentStorageStatus (*StorageHandler)(void);

/* Utility Macros */
#define ARRAY_SIZE(arr) (sizeof(arr) / sizeof((arr)[0]))
#define MIN(a, b) ((a) < (b) ? (a) : (b))
#define MAX(a, b) ((a) > (b) ? (a) : (b))
#define CLAMP(value, min_val, max_val) (MIN(MAX(value, min_val), max_val))

/* Debug and Logging Macros */
#ifdef DEBUG_ENABLED
  #define DEBUG_PRINT(fmt, ...) printf("[DEBUG] " fmt "\r\n", ##__VA_ARGS__)
  #define ERROR_PRINT(fmt, ...) printf("[ERROR] " fmt "\r\n", ##__VA_ARGS__)
  #define INFO_PRINT(fmt, ...)  printf("[INFO] " fmt "\r\n", ##__VA_ARGS__)
#else
  #define DEBUG_PRINT(fmt, ...)
  #define ERROR_PRINT(fmt, ...)
  #define INFO_PRINT(fmt, ...)
#endif

/* Memory Management Macros */
#define SAFE_FREE(ptr) do { if(ptr) { free(ptr); ptr = NULL; } } while(0)
#define ZERO_MEMORY(ptr, size) memset(ptr, 0, size)

/* Bit Manipulation Macros */
#define SET_BIT(reg, bit)     ((reg) |= (1U << (bit)))
#define CLEAR_BIT(reg, bit)   ((reg) &= ~(1U << (bit)))
#define TOGGLE_BIT(reg, bit)  ((reg) ^= (1U << (bit)))
#define CHECK_BIT(reg, bit)   (((reg) >> (bit)) & 1U)

#endif /* SYSTEM_DEFINITIONS_H */