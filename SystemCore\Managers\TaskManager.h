/**
 * @file TaskManager.h
 * @brief System task scheduler management interface
 * <AUTHOR> Systems Team
 * @date 2025-01-15
 * @copyright Copyright (c) 2025 Advanced Embedded Solutions. All rights reserved.
 * 
 * This module provides comprehensive task scheduling capabilities including
 * task registration, execution control, and priority management for embedded systems.
 */

#ifndef TASK_MANAGER_H
#define TASK_MANAGER_H

#include "SystemDefinitions.h"

/* Task Management Constants */
#define MAXIMUM_SYSTEM_TASKS             10
#define TASK_EXECUTION_INTERVAL_MS       1
#define TASK_PRIORITY_HIGH               0
#define TASK_PRIORITY_NORMAL             1
#define TASK_PRIORITY_LOW                2

/* Task State Definitions */
#define TASK_STATE_INACTIVE              0
#define TASK_STATE_ACTIVE                1
#define TASK_STATE_SUSPENDED             2

/**
 * @brief System task descriptor structure
 * @details Contains all necessary information for task management and execution
 */
typedef struct SystemTaskDescriptor
{
  SystemTaskFunction taskFunction;     /**< Task execution function pointer */
  uint32_t executionInterval;          /**< Task execution interval in milliseconds */
  uint32_t lastExecutionTime;          /**< Last execution timestamp in system ticks */
  uint8_t taskState;                   /**< Current task state (active/inactive/suspended) */
  uint8_t taskPriority;                /**< Task priority level (0=highest, 2=lowest) */
  const char* taskName;                /**< Human-readable task identification name */
} SystemTaskDescriptor;

/* Public Function Declarations */

/**
 * @brief Initialize the system task scheduler
 * @details Sets up the task management system and prepares for task execution.
 *          Configures predefined system tasks and sorts them by priority.
 * @param None
 * @return SystemConfigurationStatus Initialization result
 * @retval CONFIGURATION_SUCCESS Initialization completed successfully
 * @retval CONFIGURATION_FAILURE Initialization failed due to invalid task configuration
 * @note This function must be called before any task operations.
 *       It automatically registers all predefined system tasks.
 */
SystemConfigurationStatus InitializeSystemScheduler(void);

/**
 * @brief Execute the task scheduler main loop
 * @details Processes all registered tasks according to their schedules and priorities.
 *          Checks each task's execution interval and runs tasks that are due.
 * @param None
 * @return None
 * @note This function should be called continuously in the main loop.
 *       It handles timer overflow conditions and provides execution time monitoring.
 */
void ExecuteTaskScheduler(void);

/**
 * @brief Register a new task with the scheduler
 * @details Adds a task to the system task list with specified parameters.
 *          Tasks are automatically sorted by priority after registration.
 * @param taskFunction Pointer to the task execution function
 * @param intervalMs Task execution interval in milliseconds
 * @param priority Task priority level (0=highest, 2=lowest)
 * @param taskName Human-readable task name for debugging
 * @return SystemConfigurationStatus Registration result
 * @retval CONFIGURATION_SUCCESS Task registered successfully
 * @retval CONFIGURATION_FAILURE Registration failed (invalid parameters or task list full)
 * @note Maximum number of tasks is limited by MAXIMUM_SYSTEM_TASKS constant.
 */
SystemConfigurationStatus RegisterSystemTask(
  SystemTaskFunction taskFunction,
  uint32_t intervalMs,
  uint8_t priority,
  const char* taskName
);

/**
 * @brief Suspend a specific task
 * @details Temporarily stops task execution without removing it from the scheduler.
 *          The task can be resumed later using ResumeSystemTask().
 * @param taskIndex Index of the task to suspend (0-based)
 * @return SystemConfigurationStatus Operation result
 * @retval CONFIGURATION_SUCCESS Task suspended successfully
 * @retval CONFIGURATION_FAILURE Invalid task index
 * @note Suspended tasks remain in the task list but are skipped during execution.
 */
SystemConfigurationStatus SuspendSystemTask(uint8_t taskIndex);

/**
 * @brief Resume a suspended task
 * @details Restarts execution of a previously suspended task.
 *          Resets the last execution time to current system time.
 * @param taskIndex Index of the task to resume (0-based)
 * @return SystemConfigurationStatus Operation result
 * @retval CONFIGURATION_SUCCESS Task resumed successfully
 * @retval CONFIGURATION_FAILURE Invalid task index
 * @note The task will be scheduled for immediate execution on next scheduler run.
 */
SystemConfigurationStatus ResumeSystemTask(uint8_t taskIndex);

/**
 * @brief Get current system tick count
 * @details Returns the current system timestamp for timing calculations.
 *          This is a wrapper around HAL_GetTick() for consistency.
 * @param None
 * @return uint32_t Current system tick count in milliseconds
 * @note The returned value may overflow after approximately 49.7 days of continuous operation.
 */
uint32_t GetSystemTickCount(void);

/**
 * @brief Get total number of registered tasks
 * @details Returns the count of currently registered tasks in the scheduler.
 * @param None
 * @return uint8_t Number of registered tasks (0 to MAXIMUM_SYSTEM_TASKS)
 * @note This includes both active and suspended tasks.
 */
uint8_t GetRegisteredTaskCount(void);

/**
 * @brief Get task information by index
 * @details Retrieves task descriptor for a specific task by its index.
 *          Provides access to task configuration and state information.
 * @param taskIndex Index of the task to query (0-based)
 * @return SystemTaskDescriptor* Pointer to task descriptor or NULL if invalid index
 * @note The returned pointer is valid until the task is unregistered or scheduler is reinitialized.
 */
SystemTaskDescriptor* GetTaskDescriptor(uint8_t taskIndex);

/* Predefined System Tasks */

/**
 * @brief Status indicator task function
 * @details Updates LED status indicators based on system state
 * @param None
 * @return None
 * @note Executes every 1ms with high priority
 */
extern void UpdateStatusIndicators(void);

/**
 * @brief Analog sampling task function
 * @details Processes ADC sampling operations
 * @param None
 * @return None
 * @note Executes every 100ms with normal priority
 */
extern void ProcessAnalogSampling(void);

/**
 * @brief Input handling task function
 * @details Processes button and input events
 * @param None
 * @return None
 * @note Executes every 5ms with high priority
 */
extern void ProcessInputEvents(void);

/**
 * @brief Communication task function
 * @details Handles UART communication and command processing
 * @param None
 * @return None
 * @note Executes every 5ms with normal priority
 */
extern void ProcessCommunication(void);

/**
 * @brief OLED display task function
 * @details Updates OLED display content
 * @param None
 * @return None
 * @note Executes every 1ms with low priority
 */
extern void oled_task(void);

/**
 * @brief Sampling control task function
 * @details Manages data acquisition and sampling control
 * @param None
 * @return None
 * @note Executes every 10ms with high priority
 */
extern void ExecuteSamplingTask(void);

/* Task Management Macros */

/**
 * @brief Convert seconds to milliseconds for task intervals
 */
#define TASK_INTERVAL_SECONDS(s) ((s) * 1000)

/**
 * @brief Convert minutes to milliseconds for task intervals
 */
#define TASK_INTERVAL_MINUTES(m) ((m) * 60 * 1000)

/**
 * @brief Check if task index is valid
 */
#define IS_VALID_TASK_INDEX(idx) ((idx) < GetRegisteredTaskCount())

/**
 * @brief Get task name safely (returns "Unknown" if NULL)
 */
#define SAFE_TASK_NAME(task) ((task)->taskName ? (task)->taskName : "Unknown")

#endif /* TASK_MANAGER_H */