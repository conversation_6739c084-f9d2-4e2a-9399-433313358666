/**
 * @file TaskManager.h
 * @brief System task scheduler management interface
 * <AUTHOR> Systems Team
 * @date 2025-01-15
 * @copyright Copyright (c) 2025 Advanced Embedded Solutions. All rights reserved.
 * 
 * This module provides comprehensive task scheduling capabilities including
 * task registration, execution control, and priority management.
 */

#ifndef TASK_MANAGER_H
#define TASK_MANAGER_H

#include "SystemDefinitions.h"

/* Task Management Constants */
#define MAXIMUM_SYSTEM_TASKS             10
#define TASK_EXECUTION_INTERVAL_MS       1
#define TASK_PRIORITY_HIGH               0
#define TASK_PRIORITY_NORMAL             1
#define TASK_PRIORITY_LOW                2

/* Task State Definitions */
#define TASK_STATE_INACTIVE              0
#define TASK_STATE_ACTIVE                1
#define TASK_STATE_SUSPENDED             2

/**
 * @brief System task descriptor structure
 * @details Contains all necessary information for task management
 */
typedef struct SystemTaskDescriptor
{
  SystemTaskFunction taskFunction;     /**< Task execution function pointer */
  uint32_t executionInterval;          /**< Task execution interval in milliseconds */
  uint32_t lastExecutionTime;          /**< Last execution timestamp */
  uint8_t taskState;                   /**< Current task state */
  uint8_t taskPriority;                /**< Task priority level */
  const char* taskName;                /**< Task identification name */
} SystemTaskDescriptor;

/* Public Function Declarations */

/**
 * @brief Initialize the system task scheduler
 * @details Sets up the task management system and prepares for task execution
 * @param None
 * @return SystemConfigurationStatus Initialization result
 * @retval CONFIGURATION_SUCCESS Initialization completed successfully
 * @retval CONFIGURATION_FAILURE Initialization failed
 * @note This function must be called before any task operations
 */
SystemConfigurationStatus InitializeSystemScheduler(void);

/**
 * @brief Execute the task scheduler main loop
 * @details Processes all registered tasks according to their schedules
 * @param None
 * @return None
 * @note This function should be called continuously in the main loop
 */
void ExecuteTaskScheduler(void);

/**
 * @brief Register a new task with the scheduler
 * @details Adds a task to the system task list with specified parameters
 * @param taskFunction Pointer to the task execution function
 * @param intervalMs Task execution interval in milliseconds
 * @param priority Task priority level
 * @param taskName Human-readable task name
 * @return SystemConfigurationStatus Registration result
 * @retval CONFIGURATION_SUCCESS Task registered successfully
 * @retval CONFIGURATION_FAILURE Registration failed (task list full)
 */
SystemConfigurationStatus RegisterSystemTask(
  SystemTaskFunction taskFunction,
  uint32_t intervalMs,
  uint8_t priority,
  const char* taskName
);

/**
 * @brief Suspend a specific task
 * @details Temporarily stops task execution without removing it
 * @param taskIndex Index of the task to suspend
 * @return SystemConfigurationStatus Operation result
 */
SystemConfigurationStatus SuspendSystemTask(uint8_t taskIndex);

/**
 * @brief Resume a suspended task
 * @details Restarts execution of a previously suspended task
 * @param taskIndex Index of the task to resume
 * @return SystemConfigurationStatus Operation result
 */
SystemConfigurationStatus ResumeSystemTask(uint8_t taskIndex);

/**
 * @brief Get current system tick count
 * @details Returns the current system timestamp for timing calculations
 * @param None
 * @return uint32_t Current system tick count in milliseconds
 */
uint32_t GetSystemTickCount(void);

/**
 * @brief Get total number of registered tasks
 * @details Returns the count of currently registered tasks
 * @param None
 * @return uint8_t Number of registered tasks
 */
uint8_t GetRegisteredTaskCount(void);

/**
 * @brief Get task information by index
 * @details Retrieves task descriptor for a specific task
 * @param taskIndex Index of the task to query
 * @return SystemTaskDescriptor* Pointer to task descriptor or NULL if invalid
 */
SystemTaskDescriptor* GetTaskDescriptor(uint8_t taskIndex);

#endif /* TASK_MANAGER_H */